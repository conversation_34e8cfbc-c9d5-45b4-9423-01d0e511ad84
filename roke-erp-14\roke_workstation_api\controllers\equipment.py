# -*- coding: utf-8 -*-

from odoo import models, fields, api, http, SUPERUSER_ID, _
from bs4 import BeautifulSoup
from .common import build_tree
from datetime import datetime, timedelta
import requests
import json

import logging
_logger = logging.getLogger(__name__)


class RokeWorkstationEquipment(http.Controller):

    @http.route('/roke/workstation/equipment/read', type='json', auth='none', csrf=False, cors="*")
    def read_roke_workstation_equipment(self):
        kwargs = http.request.jsonrequest
        center_id = kwargs.get("center_id", False)

        if not center_id:
            return {"code": 1, "message": "参数错误（需要提供工位标识：center_id）", "data": None}

        center_obj = http.request.env(user=SUPERUSER_ID)['roke.work.center'].search([
            ("id", "=", int(center_id))
        ])
        if not center_obj:
            return {"code": 1, "message": "工位信息不存在或已删除。", "data": None}

        data = [
            {
                "id": equipment_id.id, "name": equipment_id.name,
                "icon": equipment_id.equipment_icon
            }
            for equipment_id in center_obj.equipment_ids
        ]

        return {"code": 0, "message": "获取工位设备列表成功", "data": data}

    @http.route('/roke/workstation/equipment/add', type='json', auth='none', csrf=False, cors="*")
    def add_roke_workstation_equipment(self):
        kwargs = http.request.jsonrequest
        center_id = kwargs.get("center_id", False)
        equipment_name = kwargs.get("equipment_name", False)
        equipment_icon = kwargs.get("equipment_icon", False)

        if not center_id:
            return {"code": 1, "message": "参数错误（需要提供工位标识：center_id）", "data": None}

        center_obj = http.request.env(user=SUPERUSER_ID)['roke.work.center'].search([
            ("id", "=", int(center_id))
        ])
        if not center_obj:
            return {"code": 1, "message": "工位信息不存在或已删除。", "data": None}

        if not equipment_name:
            return {"code": 1, "message": "参数错误（需要提供设备名称：equipment_name）", "data": None}

        workshop_obj = http.request.env(user=SUPERUSER_ID)['roke.mes.equipment'].create({
            "name": equipment_name, "equipment_icon": equipment_icon,
            "work_center_id": center_id
        })
        return {"code": 0, "message": "设备添加成功", "data": None}

    @http.route('/roke/workstation/equipment/delete', type='json', auth='user', csrf=False, cors="*")
    def delete_roke_workstation_equipment(self):
        kwargs = http.request.jsonrequest
        equipment_id = kwargs.get("equipment_id", False)

        if not equipment_id:
            return {"code": 1, "message": "参数错误（需要提供设备标识：equipment_id）", "data": None}

        equipment_obj = http.request.env(user=SUPERUSER_ID)['roke.mes.equipment'].search([
            ("id", "=", int(equipment_id))
        ])

        if not equipment_obj:
            return {"code": 1, "message": "设备信息不存在或已删除。", "data": None}

        equipment_obj.unlink()
        return {"code": 0, "message": "设备删除成功", "data": None}

    @http.route('/roke/workstation/get/tricolor_light_install_describe', type='json', auth='user', csrf=False, cors="*")
    def get_tricolor_light_install_describe(self):
        """
        获取三色灯安装说明
        """
        tricolor_light_install_describe = http.request.env['ir.config_parameter'].sudo(
        ).get_param("tricolor_light_install_describe")
        host_url = http.request.httprequest.scheme + \
            '://' + http.request.httprequest.host
        soup = BeautifulSoup(tricolor_light_install_describe, 'html.parser')
        imgs = soup.find_all('img')
        for img in imgs:
            img['src'] = host_url + img['src']
        return {"code": 0, "message": "获取三色灯安装说明成功", "data": soup.prettify()}

    # 三色灯绑定设备接口
    @http.route('/roke/workstation/equipment/bind', type='json', auth='user', csrf=False, cors="*")
    def bind_roke_workstation_equipment(self):
        kwargs = http.request.jsonrequest
        equipment_id = kwargs.get("equipment_id", False)
        plant_id = kwargs.get("plant_id", False)
        workshop_id = kwargs.get("workshop_id", False)
        work_center_id = kwargs.get("work_center_id", False)
        code = kwargs.get("code", False)
        if not code:
            return {"code": 1, "message": "三色灯编号不能为空", "data": None}
        if not equipment_id:
            return {"code": 1, "message": "设备ID不能为空", "data": None}
        # if not plant_id:
        #     return {"code": 1, "message": "车间ID不能为空", "data": None}
        # if not workshop_id:
        #     return {"code": 1, "message": "产线ID不能为空", "data": None}
        # if not work_center_id:
        #     return {"code": 1, "message": "工位ID不能为空", "data": None}

        equipment_obj = http.request.env(user=SUPERUSER_ID)['roke.mes.equipment'].search([
            ("id", "=", equipment_id)
        ])
        try:
            equipment_obj.write({
                "code": code,
                "plant_id": int(plant_id) if plant_id else False,
                "workshop_id": int(workshop_id) if workshop_id else False,
                "work_center_id": int(work_center_id) if work_center_id else False,
                "bind_date": fields.Datetime.now()
            })
            logging.info(f"绑定成功！{code}")
            return {"code": 0, "message": "绑定成功", "data": None}
        except Exception as e:
            return {"code": 1, "message": f"{e}", "data": None}

    @http.route('/roke/workstation/equipment/create', type='json', auth='none', csrf=False, cors="*")
    def create_roke_workstation_equipment(self):
        kwargs = http.request.jsonrequest
        equipment_name = kwargs.get("equipment_name", False)

        if not equipment_name:
            return {"code": 1, "message": "参数错误（需要提供设备名称：equipment_name）", "data": None}

        equipment_obj = http.request.env(user=SUPERUSER_ID)['roke.mes.equipment'].create({
            "name": equipment_name
        })
        return {"code": 0, "message": "设备添加成功", "data": {"id": equipment_obj.id,
                                                             "name": equipment_obj.name,
                                                             "code": equipment_obj.code,}}

    # 三色灯解绑设备接口
    @http.route('/roke/workstation/equipment/unbind', type='json', auth='none', csrf=False, cors="*")
    def unbind_roke_workstation_equipment(self):
        kwargs = http.request.jsonrequest
        device_code = kwargs.get("device_code", False)

        equipment_obj = http.request.env(user=SUPERUSER_ID)['roke.mes.equipment'].search([
            ("code", "=", device_code)
        ])

        try:
            equipment_obj.write({
                "code": False, "plant_id": False, "workshop_id": False, "work_center_id": False
            })
            return {"code": 0, "message": "解绑成功", "data": None}
        except Exception as e:
            return {"code": 1, "message": f"{e}", "data": None}

    @http.route('/roke/workstation/equipment/count', type='json', auth='none', csrf=False, cors="*")
    def get_roke_workstation_equipment_count(self, **kwargs):
        """
        获取设备数量
        """
        kwargs = kwargs if kwargs else http.request.jsonrequest

        center_id = kwargs.get("center_id", False)
        # if not center_id:
        #     return {"code": 1, "message": "参数错误（需要提供工位标识：center_id）", "data": None}

        model = http.request.env(user=SUPERUSER_ID)['roke.mes.equipment']
        equipments = model.search([])

        equipment_list = []
        for equipment in equipments:
            classes_ids = equipment.workshop_id.classes_id.classes_ids.filtered(
                lambda x: x.is_active
            )
            plan_run_time = 0
            for classes_item in classes_ids:
                if classes_item.end_time > classes_item.start_time:
                    duration = classes_item.end_time - classes_item.start_time
                else:
                    duration = classes_item.end_time + 24 - classes_item.start_time
                plan_run_time += duration

            equipment_list.append({
                "id": equipment.id,
                "code": equipment.code or "",
                "name": equipment.name or "",
                "center": equipment.work_center_id.name or "",
                "is_bind": True if equipment.code else False,
                "plan_run_time": plan_run_time,
            })
        binded = equipments.filtered(lambda x: x.code)
        data = {"total": len(equipments), "binded": len(binded), "equipment_list": equipment_list}
        return {"code": 0, "message": "获取设备数量成功", "data": data}

    # 性能效率
    @http.route('/roke/workstation/equipment/pe', type='json', auth='none', csrf=False, cors="*")
    def get_roke_workstation_equipment_pe(self, **kwargs):
        """
        获取设备性能效率
        """
        kwargs = kwargs if kwargs else http.request.jsonrequest
        equipment_id = kwargs.get("equipment_id", False)

        if not equipment_id:
            return {"code": 1, "message": "参数错误（需要提供设备标识：equipment_id）", "data": None}

        equipment_id = http.request.env(user=SUPERUSER_ID)['roke.mes.equipment'].search([
            ("id", "=", int(equipment_id))
        ])
        if not equipment_id.work_center_id:
            return {"code": 1, "message": "设备未绑定工位", "data": None}

        today = fields.Datetime.now()
        start_date = today.strftime('%Y-%m-%d') + " 00:00:00"
        end_date = (today + timedelta(days=1)).strftime('%Y-%m-%d') + " 00:00:00"

        work_records = http.request.env(user=SUPERUSER_ID)['roke.work.record'].search([
            ("work_center_id", "=", equipment_id.work_center_id.id),
            ("work_time", ">=", start_date), ("work_time", "<", end_date)
        ])

        product_ids = work_records.mapped("product_id")
        if len(product_ids) > 1:
            data = {"finish_qty": 0, "unqualified_qty": 0, "beet": 0}
            return {"code": 1, "message": "无法计算 实际产量 及 理论节拍时间（发现多个成品）", "data": data}
        elif len(product_ids) == 0:
            data = {"finish_qty": 0, "unqualified_qty": 0, "beet": 0}
            return {"code": 1, "message": "无法计算 实际产量 及 理论节拍时间（未发现报工数据）", "data": data}

        product_id = product_ids[0]
        capacity = product_id.capacity or 0  # 产能：单位/小时。例如：15个/小时
        if not capacity:
            data = {"finish_qty": 0, "unqualified_qty": 0, "beet": 0}
            return {"code": 1, "message": "无法计算 实际产量 及 理论节拍时间（产品未设置产能）", "data": data}
        beet = round(3600 / capacity, 2)  # 理论节拍时间：理论节拍时间 为 6 秒 / 个
        data = {
            "finish_qty": sum(work_records.mapped("finish_qty")),
            "unqualified_qty": sum(work_records.mapped("unqualified_qty")),
            "beet": beet
        }
        return {"code": 0, "message": "获取设备性能效率参数成功", "data": data}

    @http.route('/get/workstation/equipment/binded', type='json', auth='none', csrf=False, cors="*")
    def get_roke_workstation_equipment_binded(self, **kwargs):
        """
        获取已绑定三色灯的设备
        """
        domain = [('code', '!=', False)]
        _self = http.request
        light_state = _self.jsonrequest.get("light_state", False)
        if light_state:
            if light_state == '停机':
                domain = [('code', '!=', False), ('light_state', 'in', ['不可用', '连接不上'])]
            else:
                domain = [('code', '!=', False), ('light_state', '=', light_state)]
        equipment_ids = http.request.env["roke.mes.equipment"].sudo().search(domain)
        data = {}
        equipment_list = []
        for item in equipment_ids:
            equipment_list.append({
                "id": item.id,
                "code": item.code,
                "name": item.name,
                "state": item.light_state,
                "workshop_name": item.workshop_id.name or "",
                "workshop_id": item.workshop_id.id,
                "work_center_name": item.work_center_id.name or "",
                "work_center_id": item.work_center_id.id,
                "plant_name": item.plant_id.name or "",
                "plant_id": item.plant_id.id,
                "bind_date":  item.bind_date.strftime('%Y-%m-%d %H:%M:%S') if item.bind_date else ""
            })
        data["total"] = len(equipment_ids)
        data["running"] = len(equipment_ids.filtered(lambda x: x.light_state == '运行'))
        data["failure"] = len(equipment_ids.filtered(lambda x: x.light_state == '故障'))
        data["wait"] = len(equipment_ids.filtered(lambda x: x.light_state == '等待'))
        data["stop"] = len(equipment_ids.filtered(lambda x: x.light_state in ['不可用', '连接不上']))
        data["equipment_list"] = equipment_list
        return {"code": 0, "message": "获取成功", "data": data}

    @http.route('/get/workstation/equipment/modify_bind', type='json', auth='none', csrf=False, cors="*")
    def get_roke_workstation_equipment_modify_bind(self, **kwargs):
        """
        修改已绑定三色灯的设备数据
        """
        _self = http.request
        equipment_id = _self.jsonrequest.get("equipment_id", False)
        plant_id = _self.jsonrequest.get("plant_id", False)
        workshop_id = _self.jsonrequest.get("workshop_id", False)
        work_center_id = _self.jsonrequest.get("work_center_id", False)
        equipment_name = _self.jsonrequest.get("equipment_name", False)
        equipment_obj = _self.env(user=SUPERUSER_ID)['roke.mes.equipment'].search([
            ("id", "=", equipment_id)
        ])
        if not equipment_obj:
            return {"code": 1, "message": f"修改失败，没有找到相应数据", "data": None}
        data = {}
        if plant_id:
            data["plant_id"] = plant_id
        if workshop_id:
            data["workshop_id"] = workshop_id
        if work_center_id:
            data["work_center_id"] = work_center_id
        if equipment_name:
            data["name"] = equipment_name
        try:
            equipment_obj.write(data)
            return {"code": 0, "message": "修改成功", "data": None}
        except Exception as e:
            return {"code": 1, "message": f"{e}", "data": None}

    @http.route('/roke/workstation/db_uuid/get', type='json', auth='none', csrf=False, cors="*")
    def get_db_uuid(self, **kwargs):
        """
        获取账套db_uuid
        """
        data = http.request.env(user=SUPERUSER_ID)['ir.config_parameter'].get_param('database.uuid', default="")
        return {"code": 0, "message": "获取成功", "data": data}

    @http.route('/roke/workstation/equipment/get_plan_time', type='json', auth='none', csrf=False, cors="*")
    def get_plan_time(self, **kwargs):
        """
        获取对应设备的计划运行时间
        """
        _self = http.request
        equipment_obj = _self.env(user=SUPERUSER_ID)['roke.mes.equipment'].search([("code", "!=", False)])
        data = {v.code: v.plan_run_tine for v in equipment_obj}
        return {"code": 0, "message": "获取成功", "data": data}

    @http.route('/roke/workstation/equipment/get_equipment_data', type='json', auth='none', csrf=False, cors="*")
    def get_equipment_data(self, **kwargs):
        """
        获取对应设备的数据
        """
        _self = http.request
        plant_name = _self.jsonrequest.get("plant_name", False)
        domain = [("code", "!=", False)]
        if plant_name:
            domain.append(("plant_id.name", "=", plant_name))
        equipment_obj = _self.env(user=SUPERUSER_ID)['roke.mes.equipment'].search(domain)

        data = [{
            "name": v.name,
            "code": v.code,
            "plant_id": v.plant_id.id,
            "plant_name": v.plant_id.name or "",
            "workshop_id": v.workshop_id.id,
            "workshop_name": v.workshop_id.name or "",
            "work_center_id": v.work_center_id.id,
            "work_center_name": v.work_center_id.name or "",
            "category_id": v.category_id.id,
            "category_name": v.category_id.name or "",
            "category_sequence": v.category_id.sequence or 0
        } for v in equipment_obj]
        return {"code": 0, "message": "获取成功", "data": data}

    @http.route('/get/workstation/equipment/oee', type='json', auth='none', csrf=False, cors="*")
    def get_equipment_oee(self, **kwargs):
        """
        OEE设备加工率汇总表
        """
        database_uuid = http.request.env(user=SUPERUSER_ID)['ir.config_parameter'].get_param('database.uuid', default="")
        res = requests.get(f"https://dws-platform.xbg.rokeris.com/dev-api/public/device/week/{database_uuid}")
        if res.status_code != 200:
            return {"code": 1, "message": "获取数据失败", "data": None}
        res = res.json()
        data = res.get("data", [])
        domain = [('code', '!=', False)]
        plant_id = http.request.jsonrequest.get('plant_id', False)
        if plant_id:
            domain.append(('plant_id', '=', plant_id))
        name = http.request.jsonrequest.get('name', False)
        if name:
            domain.append(('name', '=', name))
        page_size = int(http.request.jsonrequest.get('page_size', 10))
        page_no = int(http.request.jsonrequest.get('page_no', 1))
        offset = (page_no - 1) * page_size
        equipment_ids = http.request.env["roke.mes.equipment"].sudo().search(domain, limit=page_size, offset=offset)
        total = http.request.env["roke.mes.equipment"].sudo().search_count(domain)
        equipment_list = []
        for equipment_id in equipment_ids:
            val_dict = {
                "id": equipment_id.id,
                "code": equipment_id.code,
                "name": equipment_id.name,
                "work_center_name": equipment_id.work_center_id.name or "",
            }
            for item in data:
                if item.get("code") == equipment_id.code:
                    for key, val in item.get("date").items():
                        val_dict[key] = val
                    break
            equipment_list.append(val_dict)
        data = {
            "total": total,
            "page_no": page_no,
            "page_size": page_size,
            "equipment_list": equipment_list,
        }
        return {"code": 0, "message": "获取成功", "data": data}
