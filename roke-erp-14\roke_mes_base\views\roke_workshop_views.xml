<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--车间产线-->
    <!--search-->
    <record id="view_roke_workshop_search" model="ir.ui.view">
        <field name="name">roke.workshop.search</field>
        <field name="model">roke.workshop</field>
        <field name="arch" type="xml">
            <search string="车间产线">
                <field name="name"/>
                <field name="code"/>
                <field name="parent_id"/>
                <separator/>
                <filter string="已归档" name="inactive" domain="[('active', '=', False)]"/>
                <group expand="0" string="Group By">
                    <filter string="上级车间产线" name="group_parent_id" context="{'group_by': 'parent_id'}"/>
                </group>
                <searchpanel>
                    <field name="parent_id" icon="fa-tags" enable_counters="1" expand="1"/>
                </searchpanel>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_workshop_tree" model="ir.ui.view">
        <field name="name">roke.workshop.tree</field>
        <field name="model">roke.workshop</field>
        <field name="arch" type="xml">
            <tree string="车间产线">
                <field name="code"/>
                <field name="name"/>
                <field name="manager_id"/>
                <field name="parent_id" optional="show"/>
                <field name="note" optional="hide"/>
                <field name="create_uid" string="创建人"/>
            </tree>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_workshop_line_tree" model="ir.ui.view">
        <field name="name">roke.workshop.line.tree</field>
        <field name="model">roke.workshop.line</field>
        <field name="arch" type="xml">
            <tree string="产线成员" editable="bottom">
                <field name="employee_id" optional="show"/>
                <field name="job_number" optional="show" readonly="1"/>
                <field name="phone" optional="show" readonly="1"/>
                <field name="team_id" optional="show" readonly="1"/>
                <field name="user_id" optional="show" readonly="1"/>
                <field name="position_id" optional="show" readonly="1"/>
                <field name="gender" optional="show" readonly="1"/>
                <field name="is_on_job" optional="show" readonly="1"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_workshop_line_form" model="ir.ui.view">
        <field name="name">roke.workshop.line.form</field>
        <field name="model">roke.workshop.line</field>
        <field name="arch" type="xml">
            <form string="产线成员">
                <group id="g1" col="4">
                    <group>
                        <field name="employee_id"/>
                        <field name="job_number"/>
                    </group>
                    <group>
                        <field name="phone"/>
                        <field name="team_id"/>
                    </group>
                    <group>
                        <field name="user_id"/>
                        <field name="position_id"/>
                    </group>
                    <group>
                        <field name="gender"/>
                        <field name="is_on_job"/>
                    </group>
                </group>
            </form>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_workshop_form" model="ir.ui.view">
        <field name="name">roke.workshop.form</field>
        <field name="model">roke.workshop</field>
        <field name="arch" type="xml">
            <form string="车间产线">
                <header/>
                <sheet>
                    <div name="button_box" class="oe_button_box">
                        <button name="show_child_action" class="oe_stat_button" icon="fa-list" type="object">
                            <field name="child_qty" widget="statinfo" string="下级数量"/>
                        </button>
                    </div>
                    <widget name="web_ribbon" text="归档" bg_color="bg-danger" attrs="{'invisible': [('active', '=', True)]}"/>
                    <group id="g1" col="4">
                        <group>
                            <field name="active" invisible="1"/>
                            <field name="id" invisible="1"/>
                            <field name="code"/>
                            <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}"/>
                        </group>
                        <group>
                            <field name="name"/>
                        </group>
                        <group>
                            <field name="manager_id" options="{'no_create': True}"/>
                        </group>
                        <group>
                            <field name="parent_id" domain="[('id', '!=', id)]"/>
                        </group>
                    </group>
                    <group id="g2">
                        <field name="note" nolabel="1" placeholder="此处可以填写备注或描述" />
                    </group>
                    <notebook>
                        <page string="班组成员">
                            <field name="employee_ids"/>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>
    <!--action-->
    <record id="view_roke_workshop_action" model="ir.actions.act_window">
        <field name="name">车间产线</field>
        <field name="res_model">roke.workshop</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{}</field>
        <field name="form_view_id" ref="view_roke_workshop_form"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
            点击左上角“创建”按钮新增一个车间产线。
            </p>
            <p>
            或者您也可以选择批量导入功能一次性导入多个车间产线。
            </p>
        </field>
    </record>

</odoo>
