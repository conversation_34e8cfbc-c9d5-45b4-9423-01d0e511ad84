# API Documentation for `/roke/equipment/info`

## 基本信息
- **接口名称**: 获取设备基础信息
- **接口描述**: 获取设备的详细信息，包括基本信息、维修记录、保养记录、点检记录和更换件记录
- **请求方式**: POST
- **认证方式**: 用户认证 (auth="user")
- **数据格式**: JSON
- **CORS支持**: 是
- **特殊用途**: 物联网灯设备信息展示

## 请求参数

### 请求头
```
Content-Type: application/json
Authorization: Bearer <token>
```

### 请求体参数
| 参数名 | 类型 | 必填 | 描述 |
|-------|------|-----|------|
| equipment_id | Integer | 是 | 设备ID |

### 请求示例
```json
{
  "equipment_id": 1
}
```

## 响应数据

### 成功响应
```json
{
  "state": "success",
  "msgs": "获取成功",
  "equipment": {
    "id": 1,
    "name": "生产设备A",
    "code": "EQ001",
    "category": "生产设备",
    "specification": "型号A-001",
    "e_state": "在用",
    "plant_name": "第一车间",
    "workshop_name": "生产线A",
    "work_center_name": "工位001",
    "user_name": "张三",
    "manufacture_date": "2023-01-15",
    "warranty_date": "2025-01-15",
    "repair_record_list": [
      {
        "id": 1,
        "report_user_id": 2,
        "report_user_name": "李四",
        "repair_user_id": 3,
        "repair_user_name": "王五",
        "report_time": "2023-06-01",
        "fault_description": "设备异响",
        "state": "已完成",
        "maintenance_scheme": "更换轴承",
        "finish_time": "2023-06-01 14:30:00",
        "equipment_name": "生产设备A",
        "priority": "高",
        "last_maintenance_date": "2023-05-01",
        "use_time": 720.5,
        "item_list": []
      }
    ],
    "maintain_record_list": [],
    "check_record_list": [],
    "change_record_list": []
  }
}
```

### 错误响应
```json
{
  "state": "error",
  "msgs": "缺少必传参数: equipment_id"
}
```

或

```json
{
  "state": "error",
  "msgs": "未查询到设备信息"
}
```

## 响应字段详细说明

### 设备基本信息
| 字段名 | 类型 | 描述 |
|-------|------|------|
| id | Integer | 设备ID |
| name | String | 设备名称 |
| code | String | 设备编号 |
| category | String | 设备类别名称 |
| specification | String | 规格型号 |
| e_state | String | 设备状态（闲置/在用/报废/报修） |
| plant_name | String | 车间名称 |
| workshop_name | String | 产线名称 |
| work_center_name | String | 工位名称 |
| user_name | String | 设备负责人姓名 |
| manufacture_date | String | 生产日期（YYYY-MM-DD，已转换时区+8小时） |
| warranty_date | String | 保修期截止日期（YYYY-MM-DD，已转换时区+8小时） |

### 维修记录 (repair_record_list)
每个维修记录包含以下字段：

| 字段名 | 类型 | 描述 |
|-------|------|------|
| id | Integer | 维修记录ID |
| report_user_id | Integer | 报修人ID |
| report_user_name | String | 报修人姓名 |
| repair_user_id | Integer | 维修人ID |
| repair_user_name | String | 维修人姓名 |
| report_time | String | 报修时间（YYYY-MM-DD，已转换时区+8小时） |
| fault_description | String | 故障描述 |
| state | String | 维修状态（通过selection字段值转换） |
| maintenance_scheme | String | 维修方案名称 |
| finish_time | String | 完成时间（YYYY-MM-DD HH:MM:SS，已转换时区+8小时） |
| equipment_name | String | 设备名称 |
| priority | String | 优先级 |
| last_maintenance_date | String | 上次维护日期（YYYY-MM-DD） |
| use_time | Number | 使用时间（小时） |
| item_list | Array | 维修项目列表（当前为空数组） |

### 保养记录 (maintain_record_list)
保养记录的数据结构与维修记录完全相同，区别在于：
- 查询条件：`type = "maintain"`
- 排序方式：按`finish_time desc`排序
- 数据来源：同样来自`roke.mes.maintenance.order`模型

### 点检记录 (check_record_list)
每个点检记录包含以下字段：

| 字段名 | 类型 | 描述 |
|-------|------|------|
| id | Integer | 点检记录ID |
| code | String | 点检记录编号 |
| check_plan_id | Integer | 点检方案ID |
| check_plan_name | String | 点检方案名称 |
| assign_user_name | String | 指派人员姓名 |
| finish_time | String | 完成时间（YYYY-MM-DD，已转换时区+8小时） |
| state | String | 点检状态 |
| description | String | 描述 |
| finish_user_id | Integer | 完成人ID |
| finish_user_name | String | 完成人姓名 |
| item_record_names | String | 点检项目名称（逗号分隔） |
| item_record_list | Array | 点检项目详细列表 |

#### 点检项目详细信息 (item_record_list)
| 字段名 | 类型 | 描述 |
|-------|------|------|
| id | Integer | 点检项目记录ID |
| check_item_id | Integer | 点检项目ID |
| check_item_name | String | 点检项目名称 |
| check_value | String | 检查值 |
| result | String | 检查结果 |
| description | String | 描述 |

### 更换件记录 (change_record_list)
每个更换件记录包含以下字段：

| 字段名 | 类型 | 描述 |
|-------|------|------|
| id | Integer | 更换记录ID |
| code | String | 更换记录编号 |
| name | String | 拆下部件名称 |
| new_name | String | 新部件名称 |
| record_date | String | 更换日期（YYYY-MM-DD，已转换时区+8小时） |
| change_user_id | Integer | 更换人ID |
| change_user_name | String | 更换人姓名 |

## 实现细节

### 数据获取逻辑
1. **参数验证**: 检查是否提供了必需的equipment_id参数
2. **设备查询**: 根据equipment_id查询设备基本信息
3. **关联数据获取**: 
   - 维修记录：调用`get_equipment_repair_list(equipment_id, "repair")`
   - 保养记录：调用`get_equipment_repair_list(equipment_id, "maintain")`
   - 点检记录：调用`get_equipment_check_list(equipment_id)`
   - 更换件记录：调用`get_equipment_change_list(equipment_id)`
4. **时间处理**: 所有时间字段都会加8小时（时区转换）
5. **数据组装**: 将所有信息组装成统一的响应格式

### 时间处理规则
```python
# 日期格式化（加8小时时区转换）
manufacture_date = (equipment.manufacture_date + datetime.timedelta(hours=8)).strftime('%Y-%m-%d')
warranty_date = (equipment.warranty_date + datetime.timedelta(hours=8)).strftime('%Y-%m-%d')

# 日期时间格式化
finish_time = (item.finish_time + datetime.timedelta(hours=8)).strftime('%Y-%m-%d %H:%M:%S')
```

### 记录数量限制
- 维修记录：最近2条（按report_time desc排序）
- 保养记录：最近2条（按finish_time desc排序）
- 点检记录：最近2条（按创建时间排序）
- 更换件记录：最近2条（按record_date desc排序）

## 使用示例

### JavaScript调用示例
```javascript
async function getEquipmentInfo(equipmentId) {
  try {
    const response = await fetch('/roke/equipment/info', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + token
      },
      body: JSON.stringify({
        equipment_id: equipmentId
      })
    });
    
    const data = await response.json();
    
    if (data.state === 'success') {
      const equipment = data.equipment;
      
      // 显示设备基本信息
      console.log(`设备名称: ${equipment.name}`);
      console.log(`设备编号: ${equipment.code}`);
      console.log(`设备状态: ${equipment.e_state}`);
      console.log(`负责人: ${equipment.user_name}`);
      console.log(`车间: ${equipment.plant_name}`);
      
      // 处理维修记录
      if (equipment.repair_record_list.length > 0) {
        console.log('最近维修记录:');
        equipment.repair_record_list.forEach(record => {
          console.log(`- ${record.report_time}: ${record.fault_description} (${record.state})`);
        });
      }
      
      // 处理点检记录
      if (equipment.check_record_list.length > 0) {
        console.log('最近点检记录:');
        equipment.check_record_list.forEach(record => {
          console.log(`- ${record.finish_time}: ${record.check_plan_name} (${record.state})`);
        });
      }
      
      return equipment;
    } else {
      console.error('获取失败:', data.msgs);
      return null;
    }
  } catch (error) {
    console.error('请求错误:', error);
    return null;
  }
}

// 使用示例
const equipment = await getEquipmentInfo(1);
```

### cURL调用示例
```bash
curl -X POST "http://localhost:8069/roke/equipment/info" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{"equipment_id": 1}'
```

## 错误处理

### 常见错误情况
1. **缺少参数**: 未提供equipment_id参数
2. **设备不存在**: 提供的equipment_id对应的设备不存在
3. **认证失败**: 用户未认证或认证过期
4. **权限不足**: 用户没有访问设备信息的权限

### 错误响应格式
```json
{
  "state": "error",
  "msgs": "具体错误描述"
}
```

## 应用场景

1. **物联网灯显示**: 在物联网设备上显示设备详细信息
2. **设备详情页面**: Web或移动端设备详情展示
3. **设备监控大屏**: 实时显示设备状态和历史记录
4. **维护管理**: 维护人员查看设备维护历史
5. **设备巡检**: 巡检人员查看设备基本信息和点检记录

## 性能考虑

1. **数据量控制**: 每类记录只返回最近2条，避免数据过多
2. **查询优化**: 建议在equipment_id、report_time、finish_time等字段上建立索引
3. **缓存策略**: 对于不经常变化的设备基本信息可以考虑缓存
4. **时区处理**: 统一的时区转换处理，确保时间显示正确

## 扩展建议

1. **分页支持**: 为各类记录添加分页参数
2. **字段选择**: 允许客户端选择需要的记录类型
3. **实时数据**: 集成实时设备运行数据
4. **图片支持**: 添加设备图片和维修图片
5. **统计信息**: 添加设备运行时间、故障率等统计数据
6. **通知集成**: 集成设备异常通知功能

## 快速参考

### 请求格式
```bash
POST /roke/equipment/info
Content-Type: application/json
Authorization: Bearer <token>

{
  "equipment_id": 1
}
```

### 响应格式
```json
{
  "state": "success|error",
  "msgs": "状态消息",
  "equipment": {
    "id": 1,
    "name": "设备名称",
    "code": "设备编号",
    "category": "设备类别",
    "e_state": "设备状态",
    "plant_name": "车间",
    "workshop_name": "产线",
    "work_center_name": "工位",
    "user_name": "负责人",
    "manufacture_date": "生产日期",
    "warranty_date": "保修期",
    "repair_record_list": [],    // 维修记录（最近2条）
    "maintain_record_list": [],  // 保养记录（最近2条）
    "check_record_list": [],     // 点检记录（最近2条）
    "change_record_list": []     // 更换件记录（最近2条）
  }
}
```

### 关键特性
- ✅ 物联网灯专用接口
- ✅ 一次请求获取设备完整信息
- ✅ 自动时区转换（+8小时）
- ✅ 每类记录限制最近2条
- ✅ 支持CORS跨域访问
- ✅ 需要用户认证

## 导入到Apifox

我已经创建了一个名为 `roke_equipment_info_api_doc_complete.json` 的文件，其中包含了完整的OpenAPI规范文档。您可以将此文件导入到Apifox中：

1. 打开Apifox
2. 点击"导入"按钮
3. 选择"OpenAPI"格式
4. 上传或粘贴`roke_equipment_info_api_doc_complete.json`文件内容
5. 完成导入

这样您就可以在Apifox中查看和测试这个API了。
