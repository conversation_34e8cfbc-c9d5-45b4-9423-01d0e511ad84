# API Documentation for `/roke/equipment/info`

## 基本信息
- **接口名称**: 获取设备基础信息
- **接口描述**: 获取设备的详细信息，包括基本信息、维修记录、保养记录、点检记录和更换件记录
- **请求方式**: POST
- **认证方式**: 用户认证 (auth="user")
- **数据格式**: JSON
- **CORS支持**: 是
- **用途**: 物联网灯设备信息展示

## 请求参数

### 请求头
```
Content-Type: application/json
Authorization: Bearer <token>
```

### 请求体参数
| 参数名 | 类型 | 必填 | 描述 |
|-------|------|-----|------|
| equipment_id | Integer | 是 | 设备ID |

### 请求示例
```json
{
  "equipment_id": 1
}
```

## 响应数据

### 成功响应
```json
{
  "state": "success",
  "msgs": "获取成功",
  "equipment": {
    "id": 1,
    "name": "生产设备A",
    "code": "EQ001",
    "category": "生产设备",
    "specification": "型号A-001",
    "e_state": "在用",
    "plant_name": "第一车间",
    "workshop_name": "生产线A",
    "work_center_name": "工位001",
    "user_name": "张三",
    "manufacture_date": "2023-01-15",
    "warranty_date": "2025-01-15",
    "repair_record_list": [
      {
        "id": 1,
        "report_user_name": "李四",
        "repair_user_name": "王五",
        "report_time": "2023-06-01",
        "fault_description": "设备异响",
        "state": "已完成",
        "finish_time": "2023-06-01 14:30:00"
      }
    ],
    "maintain_record_list": [
      {
        "id": 2,
        "report_user_name": "张三",
        "repair_user_name": "李四",
        "report_time": "2023-05-15",
        "maintenance_scheme": "定期保养",
        "state": "已完成"
      }
    ],
    "check_record_list": [
      {
        "id": 3,
        "code": "CHK001",
        "check_plan_name": "日常点检",
        "assign_user_name": "王五",
        "finish_time": "2023-06-02",
        "state": "已完成",
        "item_record_names": "温度检查,压力检查"
      }
    ],
    "change_record_list": [
      {
        "id": 4,
        "code": "CHG001",
        "name": "旧轴承",
        "new_name": "新轴承",
        "record_date": "2023-05-20",
        "change_user_name": "赵六"
      }
    ]
  }
}
```

### 错误响应
```json
{
  "state": "error",
  "msgs": "缺少必传参数: equipment_id"
}
```

或

```json
{
  "state": "error",
  "msgs": "未查询到设备信息"
}
```

## 响应字段详细说明

### 设备基本信息
| 字段名 | 类型 | 描述 |
|-------|------|------|
| id | Integer | 设备ID |
| name | String | 设备名称 |
| code | String | 设备编号 |
| category | String | 设备类别名称 |
| specification | String | 规格型号 |
| e_state | String | 设备状态（闲置/在用/报废/报修） |
| plant_name | String | 车间名称 |
| workshop_name | String | 产线名称 |
| work_center_name | String | 工位名称 |
| user_name | String | 设备负责人姓名 |
| manufacture_date | String | 生产日期（YYYY-MM-DD） |
| warranty_date | String | 保修期截止日期（YYYY-MM-DD） |

### 维修记录 (repair_record_list)
| 字段名 | 类型 | 描述 |
|-------|------|------|
| id | Integer | 维修记录ID |
| report_user_id | Integer | 报修人ID |
| report_user_name | String | 报修人姓名 |
| repair_user_id | Integer | 维修人ID |
| repair_user_name | String | 维修人姓名 |
| report_time | String | 报修时间（YYYY-MM-DD） |
| fault_description | String | 故障描述 |
| state | String | 维修状态 |
| maintenance_scheme | String | 维修方案 |
| finish_time | String | 完成时间（YYYY-MM-DD HH:MM:SS） |
| equipment_name | String | 设备名称 |
| priority | String | 优先级 |
| last_maintenance_date | String | 上次维护日期 |
| use_time | Number | 使用时间 |
| item_list | Array | 维修项目列表 |

### 保养记录 (maintain_record_list)
保养记录的数据结构与维修记录相同，但类型为"maintain"。

### 点检记录 (check_record_list)
| 字段名 | 类型 | 描述 |
|-------|------|------|
| id | Integer | 点检记录ID |
| code | String | 点检记录编号 |
| check_plan_id | Integer | 点检方案ID |
| check_plan_name | String | 点检方案名称 |
| assign_user_name | String | 指派人员姓名 |
| finish_time | String | 完成时间（YYYY-MM-DD） |
| state | String | 点检状态 |
| description | String | 描述 |
| finish_user_id | Integer | 完成人ID |
| finish_user_name | String | 完成人姓名 |
| item_record_names | String | 点检项目名称（逗号分隔） |
| item_record_list | Array | 点检项目详细列表 |

#### 点检项目详细信息
| 字段名 | 类型 | 描述 |
|-------|------|------|
| id | Integer | 点检项目记录ID |
| check_item_id | Integer | 点检项目ID |
| check_item_name | String | 点检项目名称 |
| check_value | String | 检查值 |
| result | String | 检查结果 |
| description | String | 描述 |

### 更换件记录 (change_record_list)
| 字段名 | 类型 | 描述 |
|-------|------|------|
| id | Integer | 更换记录ID |
| code | String | 更换记录编号 |
| name | String | 拆下部件名称 |
| new_name | String | 新部件名称 |
| record_date | String | 更换日期（YYYY-MM-DD） |
| change_user_id | Integer | 更换人ID |
| change_user_name | String | 更换人姓名 |

## 实现细节

### 数据获取逻辑
1. **参数验证**: 检查是否提供了必需的equipment_id参数
2. **设备查询**: 根据equipment_id查询设备基本信息
3. **关联数据**: 获取设备的各类记录（维修、保养、点检、更换件）
4. **时间处理**: 所有时间字段都会加8小时（时区转换）
5. **数据限制**: 每类记录默认只返回最近2条

### 时间处理
```python
# 日期格式化（加8小时时区转换）
manufacture_date = (equipment.manufacture_date + datetime.timedelta(hours=8)).strftime('%Y-%m-%d')
```

### 记录数量限制
- 维修记录：最近2条
- 保养记录：最近2条  
- 点检记录：最近2条
- 更换件记录：最近2条

## 使用示例

### JavaScript调用示例
```javascript
async function getEquipmentInfo(equipmentId) {
  try {
    const response = await fetch('/roke/equipment/info', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + token
      },
      body: JSON.stringify({
        equipment_id: equipmentId
      })
    });
    
    const data = await response.json();
    
    if (data.state === 'success') {
      console.log('设备信息:', data.equipment);
      return data.equipment;
    } else {
      console.error('获取失败:', data.msgs);
      return null;
    }
  } catch (error) {
    console.error('请求错误:', error);
    return null;
  }
}

// 使用示例
const equipment = await getEquipmentInfo(1);
```

### cURL调用示例
```bash
curl -X POST "http://localhost:8069/roke/equipment/info" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{"equipment_id": 1}'
```

## 错误处理

### 常见错误
1. **缺少参数**: 未提供equipment_id参数
2. **设备不存在**: 提供的equipment_id对应的设备不存在
3. **认证失败**: 用户未认证或认证过期

### 错误响应格式
```json
{
  "state": "error",
  "msgs": "错误描述信息"
}
```

## 应用场景

1. **设备详情页面**: 展示设备的完整信息
2. **物联网灯显示**: 在物联网设备上显示设备状态
3. **设备监控**: 实时查看设备的维护状态
4. **移动端应用**: 在移动设备上查看设备信息
5. **设备管理**: 设备管理人员查看设备历史记录

## 性能考虑

1. **数据量控制**: 每类记录只返回最近2条，避免数据过多
2. **查询优化**: 建议在相关字段上建立索引
3. **缓存策略**: 对于不经常变化的设备基本信息可以考虑缓存

## 扩展建议

1. **分页支持**: 为各类记录添加分页参数
2. **字段选择**: 允许客户端选择需要的记录类型
3. **实时数据**: 集成实时设备状态数据
4. **图片支持**: 添加设备图片和维修图片
5. **统计信息**: 添加设备运行统计数据

## 导入到Apifox

我已经创建了一个名为 `roke_equipment_info_api_doc.json` 的文件，其中包含了完整的OpenAPI规范文档。您可以将此文件导入到Apifox中：

1. 打开Apifox
2. 点击"导入"按钮
3. 选择"OpenAPI"格式
4. 上传或粘贴`roke_equipment_info_api_doc.json`文件内容
5. 完成导入

这样您就可以在Apifox中查看和测试这个API了。
