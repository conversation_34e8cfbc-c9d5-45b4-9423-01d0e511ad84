# -*- coding: utf-8 -*-
"""
Description:
    MES APP 设备管理相关接口
    --设备基础信息
    获取设备列表
	获取设备详情
	维修保养任务待办数
    --工装管理
    获取工装列表
	获取工装详情
	工装领用
	工装归还
	--维修
    维修任务列表
	维修任务详情
	故障报修提交
	完成维修
	延期维修
	--设备保养
    保养任务列表
	保养任务详情
	完成保养明细
	忽略保养明细
	保养明细置为等待
	完成保养
	延期保养
Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
from odoo import models, fields, api, http, SUPERUSER_ID, _
from odoo.exceptions import UserError, AccessDenied
from odoo.addons.roke_mes_base.tools import http_tool
import math
import json
import logging
import datetime
import requests
from io import StringIO
import traceback

TOKEN = "rokewx"
_logger = logging.getLogger(__name__)
headers = [('Content-Type', 'application/json; charset=utf-8')]


def get_result_name(value):
    if value == 'wait':
        return '未检查'
    elif value == 'normal':
        return '正常'
    elif value == 'anomaly':
        return '异常'
    elif value == 'fault':
        return '故障'
    else:
        return ''


def get_state_name(value):
    if value == 'wait':
        return '等待'
    elif value == 'finish':
        return '完成'
    elif value == 'cancel':
        return '取消'
    else:
        return ''


def get_normal_state_name(value):
    if value == 'normal':
        return '正常'
    elif value == 'abnormal':
        return '异常'
    else:
        return ''


def get_value_type_name(value):
    if value == 'text':
        return '文本'
    elif value == 'float':
        return '小数'
    elif value == 'int':
        return '整数'
    elif value == 'select':
        return '选择'
    else:
        return ''


def get_repair_origin(value):
    if value == 'normal':
        return '日常报修'
    elif value == 'maintain':
        return '维保报修'
    elif value == 'check':
        return '巡检报修'
    else:
        return ''


def get_plan_name(value):
    if value == 'period':
        return '定期检查'
    elif value == 'start':
        return '开班检查'
    elif value == 'random':
        return '随机检查'
    else:
        return ''


def get_user_role(user_id):

    user = http.request.env(user=SUPERUSER_ID)['res.users'].search([('id', '=', user_id)])
    role = ''

    for user_group in user.groups_id:
        if user_group.name == '设备管理员':
            role = '管理员'
        elif user_group.name == '设备报修人员':
            role = '报修人员'
        elif user_group.name == '设备派工人员':
            role = '派工人员'
        elif user_group.name == '设备作业人员':
            role = '作业人员'
        else:
            role = ''
    return role


def get_repair_domain(user_id):
    domain_list = [('id', '=', None)]

    user = http.request.env(user=SUPERUSER_ID)['res.users'].search([('id', '=', user_id)])
    for group in user.groups_id:
        if '设备' in group.name:
            if group.name == '设备管理员':
                domain_list = []
            elif group.name == '设备报修人员':
                domain_list = [('report_user_id', '=', user_id)]
            elif group.name == '设备派工人员':
                domain_list = [('state', '=', 'wait')]
            elif group.name == '设备作业人员':
                domain_list = [('user_id', '=', user_id)]
            else:
                domain_list = [('id', '=', None)]
    return domain_list


def get_repair_operation_domain(user_id):
    """
    根据用户权限返回domain
        管理员、派工人员：可查看所有的
        作业人员：可查看派工给自己的
    :param user_id: 用户ID
    :return:
    """
    user = http.request.env(user=SUPERUSER_ID)['res.users'].search([('id', '=', user_id)])
    # 查找设备管理员权限组
    equipment_manager_group = http.request.env(user=SUPERUSER_ID)['res.groups'].search([('name', '=', '设备管理员')])
    # 查找设备派工人员权限组
    equipment_dispatch_group = http.request.env(user=SUPERUSER_ID)['res.groups'].search([('name', '=', '设备派工人员')])
    # 有设备管理员权限，可以查看所有单据
    if equipment_manager_group.id in user.groups_id.ids or equipment_dispatch_group.id in user.groups_id.ids:
        # return [("state", "in", ['wait', 'assign', 'postpone'])]
        return []
    # 查找设备作业人员权限组
    equipment_operation_group = http.request.env(user=SUPERUSER_ID)['res.groups'].search([('name', '=', '设备作业人员')])
    # 有设备作业人员权限，可以查看派工给自己的单据
    if equipment_operation_group.id in user.groups_id.ids:
        # return [('user_id', '=', user_id), ("state", "in", ['assign', 'postpone'])]
        return [('user_id', '=', user_id)]
    else:
        return [('id', '=', 0)]


class ClientApiEquipment(http.Controller):

    @http.route('/roke/equipment/equipment_list', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False,
                cors='*')
    def equipment_list(self):
        """
        获取设备列表
        :param: 设备编号：设备编号
        :param: 设备编号：设备名称
        :param: 设备编号：规格型号
        :param: 是否在修
        :param: page_number: 页码
        :param: limit：每页记录数
        :return:
        """
        code = http.request.jsonrequest.get('code', False)
        name = http.request.jsonrequest.get('name', False)
        specification = http.request.jsonrequest.get('specification', False)
        in_repair = http.request.jsonrequest.get('in_repair', False)
        limit = http.request.jsonrequest.get('limit', 20)
        page_number = http.request.jsonrequest.get('page_number', 1)
        domain = []
        if code:
            domain.append(("code", "ilike", code))
        if name:
            domain.append(("name", "ilike", name))
        if specification:
            domain.append(("specification", "ilike", specification))
        if in_repair in ("是", "否"):
            if in_repair == "是":
                domain.append(("in_repair", "=", True))
            else:
                domain.append(("in_repair", "=", False))
        result = []
        _logger.info(domain)
        equipments = http.request.env['roke.mes.equipment'].search(domain)
        # page_equipments = equipments[(page_number - 1) * limit: (page_number - 1) * limit + limit]
        for equipment in equipments:
            result.append({
                "id": equipment.id,
                "code": equipment.code or "",
                "name": equipment.name or "",
                "category": equipment.category_id.name or "",
                "work_center": equipment.work_center_id.name or "",
                "manufacturer": equipment.manufacturer or "",
                "specification": equipment.specification or "",
                "in_repair": "是" if equipment.in_repair else "否",
                "e_state": equipment.e_state,
            })
        return {
            "state": "success",
            "msgs": "获取成功",
            "equipments": result,
            "total": len(equipments),
            "page_total": math.ceil(len(equipments) / limit),
            "limit": limit,
            "page_number": page_number
        }

    @http.route('/roke/equipment/equipment_detail', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False,
                cors='*')
    def equipment_detail(self):
        """
        获取设备详情
        :param: 设备ID：设备ID
        :return:
        """
        _logger.info("获取设备详情")
        equipment_id = http.request.jsonrequest.get('equipment_id', False)
        if not http_tool.check_id_valid(equipment_id):
            return {"state": "error", "msgs": "缺少入参或入参格式错误"}
        equipment = http.request.env['roke.mes.equipment'].browse(int(equipment_id))
        try:
            result = {
                "id": equipment.id or "",
                "code": equipment.code or "",
                "name": equipment.name or "",
                "category": equipment.category_id.name or "",
                "work_center": equipment.work_center_id.name or "",
                "manufacturer": equipment.manufacturer or "",
                "specification": equipment.specification or "",
                "in_repair": "是" if equipment.in_repair else "否",
                "e_state": equipment.e_state,
                "note": equipment.note or "",
                "warranty_date": equipment.warranty_date.strftime('%Y-%m-%d') if equipment.warranty_date else "",
                "location": equipment.location,
            }
        except Exception as e:
            _logger.error(e)
            _logger.error(http.request.jsonrequest)
            return {"state": "error", "msgs": "选择设备错误"}
        return {"state": "success", "msgs": "获取成功", "equipment_detail": result}

    # 由于小程序要求改为post方式 --jht
    @http.route('/roke/equipment/backlog_qty', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False,
                cors='*')
    def get_equipment_backlog_qty(self):
        """
        获取设备任务（维修/保养）待办数  气泡数量
        :return:
        """
        _logger.info("获取设备任务（维修/保养）待办数")
        domain = get_repair_domain(http.request.uid or http.request.session.uid)
        role = get_user_role(http.request.uid or http.request.session.uid)
        if role == '作业人员':
            domain += [('worker_is_read', '=', False)]
        elif role == '派工人员':
            domain += [('dispatcher_is_read', '=', False)]
        else:
            domain += []
        all_orders = http.request.env['roke.mes.maintenance.order'].search(domain)
        repair_qty = len(all_orders.filtered(lambda order: order.type == "repair" and order.state != 'finish'))
        maintain_qty = len(all_orders.filtered(lambda order: order.type == "maintain"))
        # return http.Response(json.dumps(
        #     {"state": "success", "msgs": "获取成功", "repair_qty": repair_qty, "maintain_qty": maintain_qty}
        # ), headers=headers)
        return {"state": "success", "msgs": "获取成功", "repair_qty": repair_qty, "maintain_qty": maintain_qty}

    # -----------------维保通用方法-----------------
    def _get_maintenance_order_list(self, filter_param):
        """
        获取维保单据列表
        :return:
        """
        _logger.info('--方法获取的参数--')
        _logger.info(filter_param)
        order_type = filter_param.get("order_type")
        if not order_type or order_type not in ("repair", "maintain"):
            return {"state": "error", "msgs": "单据类型错误，请联系后台相关人员"}
        domain = [("type", "=", order_type), ("maintenance_type", "=", "设备")]
        if filter_param.get("code"):
            domain.append(("code", "ilike", filter_param.get("code")))
        if filter_param.get("equipment_id"):
            domain.append(("equipment_id", "=", int(filter_param.get("equipment_id"))))
        if filter_param.get("report_time"):
            domain.append(("report_time", "=", filter_param.get("report_time")))
        if filter_param.get("e_code"):
            domain.append(("equipment_code", "=", filter_param.get("e_code")))
        # 保养任务
        if filter_param.get("create_date"):
            start_time = filter_param.get("create_date") + " 00:00:00"
            finish_time = filter_param.get("create_date") + " 23:59:59"
            domain.append(("create_date", ">", start_time))
            domain.append(("create_date", "<", finish_time))
        state = filter_param.get("state")
        if state:
            domain.append(("state", "=", state))
        # else:  # 没传状态就筛选待派工、已派工、延期的
        #     domain.append(("state", "in", ['wait', 'assign', 'postpone']))
        # if state == "未完成":
        #     domain.append(("state", "in", ("wait", "postpone", "assign")))
        # elif state == "待派工":
        #     domain.append(("state", "=", "wait"))
        # elif state == "延期":
        #     domain.append(("state", "=", "postpone"))
        # elif state == "完成":
        #     domain.append(("state", "=", "finish"))
        # elif state == "取消":
        #     domain.append(("state", "=", "cancel"))
        if order_type == "repair":
            # if filter_param.get("report_user"):
            #     domain.append(("report_user_id.name", "ilike", filter_param.get("report_user")))
            # if filter_param.get("repair_user"):
            #     domain.append(("repair_user_id.name", "ilike", filter_param.get("repair_user")))
            domain += get_repair_operation_domain(http.request.uid or http.request.session.uid)
        else:
            if filter_param.get("maintenance_scheme"):
                domain.append("|")
                domain.append(("maintenance_scheme_id.code", "ilike", filter_param.get("maintenance_scheme")))
                domain.append(("maintenance_scheme_id.name", "ilike", filter_param.get("maintenance_scheme")))
            domain += get_repair_domain(http.request.uid or http.request.session.uid)
        limit = http.request.jsonrequest.get('limit', 50)
        page_number = http.request.jsonrequest.get('page_number', 1)
        result = []
        orders = http.request.env['roke.mes.maintenance.order'].search(domain, order='report_time desc')
        page_orders = orders[(page_number - 1) * limit: (page_number - 1) * limit + limit]
        _logger.info(page_orders)
        for order in page_orders:
            r = get_repair_origin(order.repair_origin)
            result.append({
                "id": order.id,
                "code": order.code or "",
                "maintenance_type": "设备",
                "equipment": {"e_id": order.equipment_id.id, "e_code": order.equipment_id.code or "",
                              "e_name": order.equipment_id.name or "", "e_location": order.e_location or ""},
                "assign_user": order.report_user_id.name or "",
                "assign_phone": order.report_user_id.phone,
                "state": http_tool.selection_to_dict("roke.mes.maintenance.order", "state")[
                    order.state] if order.state else "",
                "priority": http_tool.selection_to_dict("roke.mes.maintenance.order", "priority")[
                    order.priority] if order.priority else "",
                "deadline": order.deadline.strftime('%Y-%m-%d') if order.deadline else "",
                "create_date": order.create_date.strftime('%Y-%m-%d') if order.create_date else "",
                "repair_origin": r,
                "fault_id": order.fault_id.id,
                "fault_name": order.fault_id.name,
                "report_user_id": order.report_user_id.id,
                "report_user_name": order.report_user_id.name,
            })
        return_data = {
            "state": "success",
            "msgs": "获取成功",
            "records": result,
            "total": len(orders),
            "page_total": math.ceil(len(orders) / limit),
            "limit": limit,
            "page_number": page_number
        }
        _logger.info('===返回的数据===')
        _logger.info(return_data)
        return return_data

    def _get_maintenance_order_detail(self, order_id, order_code, equipment_id=None):
        """
        获取维保单据详情
        :return:
        """
        # if not http_tool.check_id_valid(order_id):
        #     return {"state": "error", "msgs": "缺少入参或入参格式错误"}
        if order_id:
            if not http_tool.check_id_valid(order_id):
                return {"state": "error", "msgs": "缺少入参或入参格式错误"}
            order = http.request.env(user=SUPERUSER_ID)['roke.mes.maintenance.order'].browse(int(order_id))
        elif order_code:
            domain = get_repair_operation_domain(http.request.uid or http.request.session.uid)
            domain += [("code", "=", order_code)]
            order = http.request.env(user=SUPERUSER_ID)['roke.mes.maintenance.order'].search(domain)
            if len(order) > 1:
                return {"state": "error", "msgs": "%s获取到多个单据，请联系管理员删除重复编号的单据" % order_code}
            elif not order:
                return {"state": "error", "msgs": "%s未获取到对应单据" % order_code}
        elif equipment_id:
            result_list = []
            domain = get_repair_domain(http.request.uid or http.request.session.uid)
            domain += [("equipment_id", "=", equipment_id)]
            orders = http.request.env(user=SUPERUSER_ID)['roke.mes.maintenance.order'].search(domain)
            for order in orders:
                normal_state = get_normal_state_name(order.normal_state)
                maintenance_items = []
                for item in order.item_ids:
                    img = item.execute_files.datas if item.execute_files.datas else ""

                    maintenance_items.append({
                        "commited": False,
                        "id": item.id,
                        # "name": "%s[%s]" % (item.item_id.name, item.item_id.code),  史雅文说不需要编号让我去掉
                        "name": item.item_id.name,
                        "description": item.item_id.note or "",
                        "img": img,
                        "state": http_tool.selection_to_dict(
                            "roke.mes.maintenance.order.item", "state"
                        )[item.state] if item.state else "",
                        "is_update": item.is_update
                    })
                result_list.append({
                    "id": order.id or "",
                    "code": order.code or "",
                    "equipment": {"e_id": order.equipment_id.id, "e_code": order.equipment_id.code or "",
                                  "e_name": order.equipment_id.name or "",
                                  "e_location": order.e_location or ""},
                    # "assign_user": order.user_id.name or "",
                    "assign_user": "，".join(order.user_id.mapped("name")),
                    # "assign_phone": order.user_id.phone,
                    "assign_phone": "，".join(order.user_id.mapped("phone")),
                    "state": http_tool.selection_to_dict(
                        "roke.mes.maintenance.order", "state"
                    )[order.state] if order.state else "",
                    "priority": http_tool.selection_to_dict(
                        "roke.mes.maintenance.order", "priority"
                    )[order.priority] if order.priority else "",
                    "deadline": order.deadline.strftime('%Y-%m-%d') if order.deadline else "",
                    "create_date": order.create_date.strftime('%Y-%m-%d') if order.create_date else "",
                    "picture": order.picture,
                    "change": order.change or "",
                    "remove": order.remove or "",
                    "evaluate": order.evaluate or "",
                    "fault_id": order.fault_id.id,
                    "fault_name": order.fault_id.name,
                    "last_maintenance_date": order.last_maintenance_date.strftime(
                        '%Y-%m-%d') if order.last_maintenance_date else "",
                    "maintenance_scheme": "%s[%s]" % (
                        order.maintenance_scheme_id.name, order.maintenance_scheme_id.code),
                    "maintenance_items": maintenance_items,
                    "img": order.picture,
                    "normal_state": normal_state,
                    "repair_picture": order.repair_picture or ""
                })
            return {"state": "success", "msgs": "获取成功", "order_detail": result_list}
        else:
            return {"state": "error", "msgs": "id和code必须选择一项入参"}
        # try:
        result = {
            "id": order.id or "",
            "code": order.code or "",
            "equipment": {"e_id": order.equipment_id.id, "e_code": order.equipment_id.code or "",
                          "e_name": order.equipment_id.name or "",
                          "e_location": order.e_location or ""},
            # "assign_user": order.user_id.name or "",
            "assign_user": "，".join(order.user_id.mapped("name")),
            # "assign_phone": http_tool.get_user_phone(order.user_id.id),
            # "assign_phone": order.user_id.phone,
            "assign_phone": "，".join(order.user_id.mapped("phone")),
            "state": http_tool.selection_to_dict(
                "roke.mes.maintenance.order", "state"
            )[order.state] if order.state else "",
            "priority": http_tool.selection_to_dict(
                "roke.mes.maintenance.order", "priority"
            )[order.priority] if order.priority else "",
            "deadline": order.deadline.strftime('%Y-%m-%d') if order.deadline else "",
            "create_date": order.create_date.strftime('%Y-%m-%d') if order.create_date else "",
            "picture": order.picture,
            "change": order.change or "",
            "remove": order.remove or "",
            "evaluate": order.evaluate or "",
            "fault_id": order.fault_id.id,
            "fault_name": order.fault_id.name,
            "repair_picture": order.repair_picture or ""
        }
        # except Exception as e:
        #     _logger.error(e)
        #     return {"state": "error", "msgs": "获取单据错误"}
        # 单据状态为延期时返回说明  --update by jiaht. 2021-12-08 09:48:02
        if order.state == "postpone":
            result["postpone_description"] = order.postpone_description or ""
        if order.type == "repair":
            # 报修信息
            result["report_time"] = (order.report_time + datetime.timedelta(hours=8)).strftime(
                '%Y-%m-%d %H:%M:%S') if order.report_time else ""
            result["report_user"] = order.report_user_id.name or ""
            result["report_phone"] = order.report_user_id.phone
            result["fault_description"] = order.fault_description or ""
            result["fault_files"] = http_tool.get_image_data(order.fault_files.ids)
            # 维修信息
            result["repair_user"] = order.repair_user_id.name or ""
            result["repair_phone"] = order.repair_user_id.phone
            result["repair_description"] = order.repair_description or ""
            result["repair_files"] = http_tool.get_image_data(order.repair_files.ids)
            result["repair_origin"] = order.repair_origin or ""
            result["level"] = order.level or ""
            # result["finish_time"] = order.write_date if order.state == 'finish' else ""
            result["finish_time"] = (order.write_date + datetime.timedelta(hours=8)).strftime(
                '%Y-%m-%d %H:%M:%S') if order.state == 'finish' else ""

        else:
            result["last_maintenance_date"] = order.last_maintenance_date.strftime(
                '%Y-%m-%d') if order.last_maintenance_date else ""
            # 史雅文说不要项目编号，去掉了
            result["maintenance_scheme"] = "%s[%s]" % (
                order.maintenance_scheme_id.name, order.maintenance_scheme_id.code)
            # result["maintenance_scheme"] = order.maintenance_scheme_id.name
            result["repair_user"] = order.repair_user_id.name or ""
            maintenance_items = []
            for item in order.item_ids:
                img = item.execute_files.datas if item.execute_files.datas else ""

                maintenance_items.append({
                    "commited": False,
                    "id": item.id,
                    # "name": "%s[%s]" % (item.item_id.name, item.item_id.code),  史雅文说不需要编号让我去掉
                    "name": item.item_id.name,
                    "description": item.item_id.note or "",
                    "img": img,
                    "state": http_tool.selection_to_dict(
                        "roke.mes.maintenance.order.item", "state"
                    )[item.state] if item.state else "",
                    "is_update": item.is_update,
                    "execute_time": item.execute_time.strftime(
                        '%Y-%m-%d %H:%M:%S') if item.execute_time else "",
                    "execute_user_name": item.execute_user_id.name or "",

                })
            result["maintenance_items"] = maintenance_items
            result["img"] = order.picture
            normal_state = get_normal_state_name(order.normal_state)
            result["normal_state"] = normal_state
        return {"state": "success", "msgs": "获取成功", "order_detail": result}

    def _execute_maintenance_order(self, params):
        """
        维修/保养单据完成/延期
        :return:
        """
        order_id = params.get("code")
        code = order_id
        target_state = params.get("target_state")
        description = params.get("description")
        name = params.get("name")
        new_name = params.get("new_name")
        file_datas = params.get("file_datas", [])
        repair_picture = params.get("repair_picture", [])
        repair_origin = params.get('repair_origin', False)
        e_location = params.get('e_location', False)
        phone = params.get('phone', False)
        fault_id = params.get('fault_id', False)

        # if file_datas and type(file_datas) not in (list,):
        #     return {"state": "error", "msgs": "附件格式错误，请以list形式上传"}
        # if not http_tool.check_id_valid(order_id):
        #     return {"state": "error", "msgs": "缺少入参或入参格式错误"}
        # order = http.request.env['roke.mes.maintenance.order'].browse(int(order_id))
        order = http.request.env['roke.mes.maintenance.order'].search([('code', '=', code)])
        # 校验保养任务下的保养明细是否全部完成
        if target_state == "finish" and order.type == "maintain" and order.item_ids.filtered(
                lambda item: item.state == "wait"):
            return {"state": "error", "msg": "保养项目必须全部处理后才可以完成当前保养任务"}
        order.execute_order(state=target_state, description=description)
        # 处理维修结果照片
        user_id = None
        if params.get('user_id'):
            user_id = params.get('user_id')
        if file_datas:
            attachment_ids = http_tool.create_image_attachment("WX" + order.code, file_datas)
            order.write({
                "repair_files": [(6, 0, attachment_ids)],
            })
        if repair_picture:
            order.write({
                "repair_picture": repair_picture,
            })
        order.write({
            "repair_user_id": int(user_id),
            "repair_origin": repair_origin,
            "e_location": e_location,
            "phone": phone,
            "fault_id": int(fault_id),
        })
        if order.type == "repair":
            try:
                order.send_wx_message_finish_repair()
            except Exception as e:
                _logger.info(e)

        # 存在拆下换上件就更新换件记录
        if name and new_name:
            http.request.env["roke.mes.equipment.change.record"].create({
                "name": name,
                "new_name": new_name,
                "record_date": fields.Datetime.now(),
                "e_id": order.equipment_id.id
            })
            order.write({"change": new_name, "remove": name})
        return {"state": "success", "msgs": "操作成功"}

    def _execute_maintenance_order_item(self, params):
        """
        维修/保养单据完成/延期
        :return:
        """
        order_item_id = params.get("order_item_id")
        target_state = params.get("target_state")
        description = params.get("description")
        file_datas = params.get("file_datas", [])
        if file_datas and type(file_datas) not in (list,):
            return {"state": "error", "msgs": "附件格式错误，请以list形式上传"}
        if not http_tool.check_id_valid(order_item_id):
            return {"state": "error", "msgs": "缺少入参或入参格式错误"}
        order_item = http.request.env['roke.mes.maintenance.order.item'].browse(int(order_item_id))
        order_item.execute(state=target_state, description=description)
        # 处理保养结果照片
        attachment_ids = http_tool.create_image_attachment("BY%s-%s" % (order_item.order_id.code, str(order_item.id)),
                                                           file_datas)
        if file_datas:
            order_item.write({"execute_files": [(6, 0, attachment_ids)]})
        return {"state": "success", "msgs": "操作成功"}

    # -----------------设备维修-----------------
    @http.route('/roke/equipment/repair_list', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False,
                cors='*')
    def repair_list(self):
        """
        获取维修任务列表
        :param: code：单据编号
        :param: state：单据状态
        :param: report_user：报修人
        :param: repair_user：维修人
        :param: page_number: 页码
        :param: limit：每页记录数
        :return:
        """
        _logger.info("获取维修任务列表")
        http.request.jsonrequest["order_type"] = "repair"
        if http.request.jsonrequest.get('flag', False) == 'record':
            return self._get_repair_record_list(http.request.jsonrequest)
        else:
            return self._get_maintenance_order_list(http.request.jsonrequest)

    @http.route('/roke/equipment/repair_detail', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False,
                cors='*')
    def repair_detail(self):
        """
        获取维修任务详情
        :param: order_id：单据ID
        :return:
        """
        _logger.info("获取维修任务详情")
        _logger.info(http.request.jsonrequest)
        return self._get_maintenance_order_detail(http.request.jsonrequest.get('order_id', False),
                                                  http.request.jsonrequest.get('code'))

    @http.route('/roke/equipment/finish_repair', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False,
                cors='*')
    def finish_repair(self):
        """
        完成维修任务
        :param: order_id：单据ID
        :param: description：维修描述
        :param: file_datas：上传照片
        :return:
        """
        _logger.info("完成维修任务")
        http.request.jsonrequest["target_state"] = "finish"
        http.request.jsonrequest["user_id"] = http.request.uid or http.request.session.uid
        return self._execute_maintenance_order(http.request.jsonrequest)

    @http.route('/roke/equipment/postpone_repair', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False,
                cors='*')
    def postpone_repair(self):
        """
        延期维修任务
        :param: order_id：单据ID
        :param: description：延期描述
        :return:
        """
        _logger.info("延期维修任务")
        http.request.jsonrequest["target_state"] = "postpone"
        return self._execute_maintenance_order(http.request.jsonrequest)

    @http.route('/roke/equipment/get_repair_code', type='http', methods=['GET'], auth="user", csrf=False,
                cors='*')
    def get_repair_next_code(self):
        """
        报修获取报修单号
        :return:
        """
        _logger.info("报修获取报修单号")
        code = http.request.env['ir.sequence'].next_by_code('roke.mes.repair.code')
        return http.Response(json.dumps({"state": "success", "msgs": "获取成功", "code": code}), headers=headers)

    @http.route('/roke/equipment/failure_submit', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False,
                cors='*')
    def failure_submit(self):
        """
        设备报修
        :param: code：报修编号
        :param: equipment_id：设备ID
        :param: description：故障描述
        :param: file_datas：上传照片
        :param: repair_origin：报修来源
        :param: fault_id：故障类型ID
        :param: phone：联系电话
        :param: e_location：设备地点
        :return:
        """
        _logger.info("设备报修")
        equipment_id = http.request.jsonrequest.get('equipment_id', False)
        repair_origin = http.request.jsonrequest.get('repair_origin', False)
        e_location = http.request.jsonrequest.get('e_location', False)
        phone = http.request.jsonrequest.get('phone', False)
        fault_id = http.request.jsonrequest.get('fault_id', False)
        file_datas = http.request.jsonrequest.get("file_datas", [])
        # if file_datas and type(file_datas) not in (list,):
        #     return {"state": "error", "msgs": "附件格式错误，请以list形式上传"}
        picture = http.request.jsonrequest.get('picture', False)

        if not http_tool.check_id_valid(equipment_id):
            return {"state": "error", "msgs": "缺少入参或入参格式错误"}
        equipment = http.request.env["roke.mes.equipment"].browse(int(equipment_id))
        try:
            order = http.request.env["roke.mes.maintenance.order"].create({
                # "code": http.request.jsonrequest.get('code', '新建'),
                "equipment_id": equipment.id,
                "type": "repair",
                "fault_description": http.request.jsonrequest.get('description', ''),
                "priority": http.request.jsonrequest.get('priority', 'normal'),
                "deadline": http.request.jsonrequest.get('deadline') if http.request.jsonrequest.get('deadline') else False,
                "repair_origin": repair_origin,
                "fault_id": fault_id,
                "phone": phone,
                "e_location": e_location,
            })
            equipment.write({"in_repair": True, "e_state": "报修"})
            # 处理故障照片
            if file_datas:
                attachment_ids = http_tool.create_image_attachment("GZ" + order.code, file_datas)
                order.write({"fault_files": [(6, 0, attachment_ids)]})
            if picture:
                order.write({"picture": picture})
            try:
                order.send_wx_message_submit_repair()
            except Exception as e:
                _logger.info(e)
            return {"state": "success", "msgs": "报修成功", "repair_order_id": order.id}
        except Exception as e:
            _logger.error(e)
            return {"state": "error", "msgs": "入参错误"}

    # -----------------设备保养-----------------
    @http.route('/roke/equipment/maintain_list', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False,
                cors='*')
    def maintain_list(self):
        """
        获取保养任务列表
        :param: code：单据编号
        :param: state：单据状态
        :param: maintenance_scheme：保养方案编号或名称
        :param: page_number: 页码
        :param: limit：每页记录数
        :return:
        """
        _logger.info("获取保养任务列表")
        http.request.jsonrequest["order_type"] = "maintain"
        return self._get_maintenance_order_list(http.request.jsonrequest)

    @http.route('/roke/equipment/maintain_detail', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False,
                cors='*')
    def maintain_detail(self):
        """
        获取保养任务详情
        :param: order_id：单据ID
        :return:
        """
        _logger.info("获取保养任务详情")
        return self._get_maintenance_order_detail(http.request.jsonrequest.get('order_id', False),
                                                  http.request.jsonrequest.get('order_code', False),
                                                  http.request.jsonrequest.get('equipment_id', False))

    @http.route('/roke/equipment/maintain_item_detail', type='json', methods=['POST', 'OPTIONS'], auth="user",
                csrf=False,
                cors='*')
    def maintain_item_detail(self):
        """
        获取保养单项目明细详情
        :param: order_item_id：明细ID
        :return:
        """
        order_item_id = http.request.jsonrequest.get('order_item_id', False)
        if not http_tool.check_id_valid(order_item_id):
            return {"state": "error", "msgs": "缺少入参或入参格式错误"}
        order_item = http.request.env['roke.mes.maintenance.order.item'].browse(order_item_id)
        try:
            result = {
                "id": order_item.id,
                "name": "%s[%s]" % (order_item.item_id.name, order_item.item_id.code),
                "description": order_item.item_id.note,
                "state": http_tool.selection_to_dict(
                    "roke.mes.maintenance.order.item", "state"
                )[order_item.state] if order_item.state else "",
                "execute_user": order_item.execute_user_id.name or "",
                "execute_phone": order_item.execute_user_id.phone,
                "execute_time": order_item.execute_time.strftime(
                    '%Y-%m-%d %H:%M:%S') if order_item.execute_time else "",
                "execute_files": http_tool.get_image_data(order_item.execute_files.ids),
                "execute_description": order_item.description or ""
            }
            return {"state": "success", "msgs": "获取成功", "order_item_detail": result}
        except Exception as e:
            _logger.error(e)
            return {"state": "error", "msgs": "获取单据错误"}

    @http.route('/roke/equipment/finish_maintain_item', type='json', methods=['POST', 'OPTIONS'], auth="user",
                csrf=False,
                cors='*')
    def finish_maintain_item(self):
        """
        完成保养单项目明细
        :param: order_item_id：项目明细ID
        :param: description：描述
        :param: file_datas：照片
        :return:
        """
        _logger.info("完成保养任务明细")
        http.request.jsonrequest["target_state"] = "finish"
        return self._execute_maintenance_order_item(http.request.jsonrequest)

    @http.route('/roke/equipment/ignore_maintain_item', type='json', methods=['POST', 'OPTIONS'], auth="user",
                csrf=False,
                cors='*')
    def ignore_maintain_item(self):
        """
        忽略保养单项目明细
        :param: order_item_id：项目明细ID
        :param: description：描述
        :return:
        """
        _logger.info("忽略保养任务明细")
        http.request.jsonrequest["target_state"] = "ignore"
        return self._execute_maintenance_order_item(http.request.jsonrequest)

    @http.route('/roke/equipment/maintain_item_set_wait', type='json', methods=['POST', 'OPTIONS'], auth="user",
                csrf=False,
                cors='*')
    def maintain_item_set_wait(self):
        """
        保养单项目明细置为等待
        :param: order_item_id：项目明细ID
        :param: description：描述
        :return:
        """
        _logger.info("保养任务明细置为等待")
        http.request.jsonrequest["target_state"] = "wait"
        return self._execute_maintenance_order_item(http.request.jsonrequest)

    @http.route('/roke/equipment/finish_maintain', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False,
                cors='*')
    def finish_maintain(self):
        """
        完成保养任务
        :param: order_id：单据ID
        :param: description：维修描述
        :return:
        """
        _logger.info("完成保养任务")
        http.request.jsonrequest["target_state"] = "finish"
        return self._execute_maintenance_order(http.request.jsonrequest)

    @http.route('/roke/equipment/postpone_maintain', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False,
                cors='*')
    def postpone_maintain(self):
        """
        延期保养任务
        :param: order_id：单据ID
        :param: description：延期描述
        :return:
        """
        _logger.info("延期保养任务")
        http.request.jsonrequest["target_state"] = "postpone"
        return self._execute_maintenance_order(http.request.jsonrequest)

    # -----------------设备巡检-----------------
    @http.route('/roke/mes/get_check_list', type='json', methods=['POST', 'OPTIONS'], auth="none", csrf=False, cors='*')
    def get_check_list(self):
        """
        获取巡检记录
        :return:
        """
        create_date = http.request.jsonrequest.get('create_date', False)
        state = http.request.jsonrequest.get('state', False)
        code = http.request.jsonrequest.get('code', False)
        domain_list = []
        if code:
            domain_list.append(('code', '=', code))
        if create_date:
            start_time = create_date + " 00:00:00"
            finish_time = create_date + " 23:59:59"
            domain_list.append(("create_date", ">", start_time))
            domain_list.append(("create_date", "<", finish_time))
        if state:
            domain_list.append(('state', '=', state))
        res = http.request.env(user=SUPERUSER_ID)['roke.mes.eqpt.spot.check.record'].search(domain_list)
        data_list = []
        for record in res:
            if record.state == 'wait':
                state = '等待'
            elif record.state == 'finish':
                state = '完成'
            elif record.state == 'cancel':
                state = '取消'
            else:
                state = ''

            data_list.append({
                "id": record.id,
                "scheme_id": record.check_plan_id.id,
                "code": record.code,
                "name": record.check_plan_id.name,
                "equipment_id": record.equipment_id.id or "",
                "equipment_code": record.equipment_id.code or "",
                "equipment_name": record.equipment_id.name or "",
                "equipment_location": record.equipment_id.location or "",
                "create_date": record.create_date.date(),
                "state": state,
                "plan_type": record.check_plan_id.type,
                "plan_type_name": get_plan_name(record.check_plan_id.type),
                "operator": record.finish_user_id.id,
                "operator_name": record.finish_user_id.name
            })
        return {"state": "success", "msgs": "获取成功", "records": data_list}

    @http.route('/roke/mes/get_check_detail', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False,
                cors='*')
    def get_check_detail(self):
        """
        根据id获取巡检作业
        :return:
        """
        order_id = http.request.jsonrequest.get('order_id', False)
        request = http.request.env(user=SUPERUSER_ID)
        user_id = http.request.uid or http.request.session.uid
        # 根据设备编号查询巡检方案
        order = request['roke.mes.eqpt.spot.check.record'].search([('id', '=', order_id)])
        if not order:
            return {"state": "error", "msgs": "未查询到巡检记录"}
        # 获取状态中文
        state = get_state_name(order.state)
        # 获取巡检明细
        line_list = []
        lines = request['roke.mes.eqpt.spot.check.line'].search([('record_id', '=', int(order.id))])
        for line in lines:
            result = get_result_name(line.result)
            value_type = get_value_type_name(line.input_type_id.input_type)
            line_list.append({
                "id": line.id,
                "name": line.check_item_id.name,
                "description": line.description or "",
                "result": result,
                "value_type": value_type,
                "check_value": line.check_value or "",
                "standard_value": line.standard_value,
                "is_update": line.is_update
            })
        normal_state = get_normal_state_name(order.normal_state)
        order_detail = {
            "id": order.id,
            "code": order.code,
            "normal_state": normal_state,
            "equipment": {
                "e_id": order.equipment_id.id or "",
                "e_code": order.equipment_id.code or "",
                "e_name": order.equipment_id.name or "",
                "e_location": order.equipment_id.location or "",
            },
            "user_id": user_id,
            "state": state,
            "check_lines": line_list,
            "picture": order.picture,
            "description": order.description
        }

        return {"state": "success", "msgs": "获取成功", "records": order_detail}

    @http.route('/roke/mes/get_check_operation', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False,
                cors='*')
    def get_check_operation(self):
        """
        根据设备id获取方案信息
        :return:
        """
        code = http.request.jsonrequest.get('code', False)
        request = http.request.env(user=SUPERUSER_ID)
        user_id = http.request.uid or http.request.session.uid
        _logger.info('设备编码：%s' % code)
        # 根据设备编号查询巡检方案
        plan = request['roke.mes.eqpt.spot.check.plan'].search([('equipment_id.code', '=', code)], limit=1)
        if not plan:
            return {"state": "error", "msgs": "未查询到巡检方案"}
        # 如果是定检查询巡检记录id
        record_id = ""
        order_code = ""
        order_date = ""
        order_state = ""
        if plan.type == 'period':
            record = request['roke.mes.eqpt.spot.check.record'].search([('check_plan_id', '=', plan.id),
                                                                        ('state', '!=', 'finish')], limit=1)
            record_id = record.id
            order_code = record.code
            order_date = record.create_date
            order_state = record.state
            if not record_id:
                return {"state": "error", "msgs": "未查询到此方案的定检记录【%s】" % plan.code}
        # 获取状态中文
        # state = get_state_name(plan.state)
        # 获取巡检明细
        line_list = []
        for line in plan.check_item_ids:
            # result = get_result_name(line.result)
            value_type = get_value_type_name(line.input_type_id.input_type)
            #
            select_item = []
            for item in line.select_item_ids:
                select_item.append({
                    "value": item.value
                })
            # 选择项
            line_list.append({
                "id": line.id,
                "name": line.name,
                "description": line.description or "",
                "value_type": value_type,
                "standard_value": line.standard_value or "",
                "select_item": select_item
            })
        # normal_state = get_normal_state_name(order.normal_state)
        order_detail = {
            "id": plan.id,
            "equipment": {
                "e_id": plan.equipment_id.id or "",
                "e_code": plan.equipment_id.code or "",
                "e_name": plan.equipment_id.name or "",
                "e_location": plan.equipment_id.location or "",
            },
            "user_id": user_id,
            "check_lines": line_list,
            "plan_type": plan.type,
            "order_id": record_id,
            "order_code": order_code,
            "order_date": order_date,
            "order_state": order_state,
            "order_state_name": get_state_name(order_state),
        }
        res = {"state": "success", "msgs": "获取成功", "records": order_detail}
        _logger.info(res)
        return res

    @http.route('/roke/mes/submit_check_detail', type='json', methods=['POST', 'OPTIONS'], auth="user",
                csrf=False, cors='*')
    def submit_check_detail(self):
        """
        提交巡检详情
        :return:
        """
        import uuid
        detail = http.request.jsonrequest.get('detail_id', False)
        description = http.request.jsonrequest.get('description', False)
        picture = http.request.jsonrequest.get('picture', False)
        check_value = http.request.jsonrequest.get('check_value', False)
        if not detail:
            return {"state": "error", "msgs": "未查询到数据"}
        item = http.request.env(user=SUPERUSER_ID)['roke.mes.eqpt.spot.check.line'].search([('id', '=', int(detail))])
        if not item:
            return {"state": "error", "msgs": "未查询到数据"}
        attachment_ids = None
        if picture:
            attachment_ids = http_tool.create_image_attachment(uuid.uuid4(), [picture])
            attachment_ids = [(6, 0, attachment_ids)]
        item.write({
            "check_value": check_value,
            "description": description,
            "attachment_ids": attachment_ids,
            "is_update": True
        })
        return {"state": "success", "msgs": "提交成功"}

    @http.route('/roke/mes/submit_check_order', type='json', methods=['POST', 'OPTIONS'], auth="user",
                csrf=False, cors='*')
    def submit_check_order(self):
        """
        提交巡检单
        :return:
        """
        item_list = http.request.jsonrequest.get('item_list', False)
        _logger.info('获取到的明细为：%s' % item_list)
        maintenance_id = http.request.jsonrequest.get('order_id', False)
        _logger.info('获取到的单据id为：%s' % maintenance_id)
        picture = http.request.jsonrequest.get('picture', False)
        description = http.request.jsonrequest.get('description', False)
        user_id = http.request.uid or http.request.session.uid
        request = http.request.env(user=user_id)
        order_obj = request['roke.mes.eqpt.spot.check.record'].search([('id', '=', maintenance_id)])
        _logger.info('查询到的定检记录%s' % order_obj)
        if not order_obj:
            return {"state": "error", "msgs": "未查询到巡检记录"}
        if order_obj.state == 'finish':
            return {"state": "error", "msgs": "该巡检记录已是完成状态，不可再次修改"}
        if item_list:
            for item in item_list:
                item_obj = request['roke.mes.eqpt.spot.check.line'].search(
                    [('id', '=', item.get('check_item_id')),
                     ('record_id', '=', int(maintenance_id))])
                _logger.info([('check_item_id', '=', item.get('check_item_id')),
                     ('record_id', '=', int(maintenance_id))])
                _logger.info(item_obj)
                check_value = item.get('check_value')
                if not check_value:
                    return {"state": "error", "msgs": "缺少判定结果"}
                item_obj.write({
                    "check_value": check_value,
                })
        order_obj.write({
            "picture": picture,
            "state": "finish",
            "finish_user_id": user_id,
            "finish_time": fields.Datetime.now(),
            "description": description
        })
        return {"state": "success", "msgs": "提交成功"}

    @http.route('/roke/mes/submit_check_order_create', type='json', methods=['POST', 'OPTIONS'], auth="user",
                csrf=False, cors='*')
    def submit_check_order_create(self):
        """
        提交巡检单
        :return:
        """
        item_list = http.request.jsonrequest.get('item_list', False)
        check_plan_id = http.request.jsonrequest.get('check_plan_id', False)
        equipment_id = http.request.jsonrequest.get('equipment_id', False)
        picture = http.request.jsonrequest.get('picture', False)
        description = http.request.jsonrequest.get('description', False)
        user_id = http.request.uid or http.request.session.uid
        request = http.request.env(user=user_id)
        if not item_list:
            return {"state": "error", "msgs": "参数错误"}
        # 创建巡检记录
        order_obj = request['roke.mes.eqpt.spot.check.record'].create({
            "check_plan_id": int(check_plan_id),
            "equipment_id": int(equipment_id),
            "picture": picture,
            "state": "finish",
            "finish_user_id": user_id,
            "finish_time": fields.Datetime.now(),
            "description": description,
            "is_api": True
        })
        # 创建巡检明细
        for item in item_list:
            request['roke.mes.eqpt.spot.check.line'].create({
                "record_id": order_obj.id,
                "check_item_id": item.get('check_item_id'),
                "check_value": item.get('check_value'),
            })
        return {"state": "success", "msgs": "提交成功"}

    @http.route('/roke/mes/submit_maintenance_item', type='json', methods=['POST', 'OPTIONS'], auth="user",
                csrf=False, cors='*')
    def submit_maintenance_item(self):
        """
        提交保养项目
        :return:
        """
        import uuid
        detail = http.request.jsonrequest.get('detail_id', False)
        description = http.request.jsonrequest.get('description', False)
        picture = http.request.jsonrequest.get('picture', False)
        if not detail:
            return {"state": "error", "msgs": "未查询到数据"}
        item = http.request.env(user=SUPERUSER_ID)['roke.mes.maintenance.order.item'].search([('id', '=', int(detail))])
        if not item:
            return {"state": "error", "msgs": "未查询到数据"}

        attachment_ids = http_tool.create_image_attachment(uuid.uuid4(), [picture])
        item.write({
            "description": description,
            "execute_files": [(6, 0, attachment_ids)],
            "execute_time": fields.Datetime.now(),
            "is_update": True
        })
        return {"state": "success", "msgs": "提交成功"}

    @http.route('/roke/mes/submit_maintenance', type='json', methods=['POST', 'OPTIONS'], auth="user",
                csrf=False, cors='*')
    def submit_maintenance(self):
        """
        提交保养详情
        :return:
        """
        item_list = http.request.jsonrequest.get('item_list', False)
        maintenance_id = http.request.jsonrequest.get('order_id', False)
        picture = http.request.jsonrequest.get('picture', False)
        user_id = http.request.uid or http.request.session.uid
        request = http.request.env(user=user_id)
        if item_list:
            for item in item_list:
                item_obj = request['roke.mes.maintenance.order.item'].search([('id', '=', item.get('id'))])
                item_obj.write({
                    "state": item.get('state')
                })
        order_obj = request['roke.mes.maintenance.order'].search([('id', '=', maintenance_id)])
        order_obj.write({
            "picture": picture,
            "state": "finish"
        })
        return {"state": "success", "msgs": "提交成功"}

    @http.route('/roke/mes/get_users_info', type='json', methods=['POST', 'OPTIONS'], auth="user",
                csrf=False, cors='*')
    def get_users_info(self):
        """
        获取用户信息
        :return:
        """
        user_list = []
        users = http.request.env['res.users'].search([])
        for user in users:
            user_list.append({
                "user_id": user.id,
                "name": user.name
            })
        return {"state": "success", "msgs": "提交成功", "records": user_list}

    @http.route('/roke/mes/get_fault_info', type='json', methods=['POST', 'OPTIONS'], auth="user",
                csrf=False, cors='*')
    def get_fault_info(self):
        """
        获取故障类型
        :return:
        """
        user_list = []
        faults = http.request.env['roke.mes.equipment.fault'].search([])
        for fault in faults:
            user_list.append({
                "fault_id": fault.id,
                "name": fault.name
            })
        return {"state": "success", "msgs": "提交成功", "records": user_list}

    @http.route('/roke/mes/repair_dispatch', type='json', methods=['POST', 'OPTIONS'], auth="user",
                csrf=False, cors='*')
    def repair_dispatch(self):
        """
        维修派工
        :return:
        """
        user_list_ = http.request.jsonrequest.get('user_list', False)
        order_id = int(http.request.jsonrequest.get('order_id', False))
        user_list = [int(user) for user in user_list_]
        order = http.request.env['roke.mes.maintenance.order'].search([('id', '=', int(order_id))], limit=1)
        _logger.info(http.request.jsonrequest)
        if not order or not user_list:
            return {"state": "error", "msgs": "未查询到单据"}
        order.write({
            "user_id": [(6, 0, user_list)],
            "state": "assign"
        })
        try:
            order.send_wx_message()
        except Exception as e:
            _logger.info(e)

        return {"state": "success", "msgs": "提交成功"}

    @http.route('/roke/wx/get_logo', type='json', methods=['POST', 'OPTIONS'], auth="none",
                csrf=False, cors='*')
    def wx_get_logo(self):
        """
        查询公司logo返回
        :return:
        """
        company_id = http.request.jsonrequest.get('company_id', 1)
        order = http.request.env['res.company'].search([('id', '=', company_id)], limit=1)
        return {"state": "success", "msgs": "查询成功", "logo": order.logo}

    @http.route('/roke/wx_auth', type='http', methods=['GET', 'POST', 'OPTIONS'], auth="none",
                csrf=False, cors='*')
    def wx_auth(self, **kwargs):
        """
        微信后台验证
        :param kwargs:
        :return:
        """
        import hashlib
        _logger.info('进入微信后台校验')
        _logger.info(kwargs)
        token = kwargs.get('token', TOKEN)
        timestamp = kwargs.get('timestamp')
        nonce = kwargs.get('nonce')
        signature = kwargs.get('signature')
        echostr = kwargs.get('echostr')
        sha_list = [token, timestamp, nonce]
        sha_list.sort()
        sha_str = "".join(sha_list)
        sha = hashlib.sha1(sha_str.encode('utf-8'))
        sha1 = sha.hexdigest()
        if sha1 == signature:
            return http.Response(echostr, headers=headers)
        else:
            return http.Response("fail", headers=headers)

    # @http.route('/roke/get_openid', type='json', methods=['GET', 'POST', 'OPTIONS'], auth="none",
    #             csrf=False, cors='*')
    # def get_openid(self):
    #     """
    #     获取open_id
    #     :param kwargs:
    #     :return:
    #     """
    #     appid = http.request.jsonrequest.get('appid')
    #     secret = http.request.jsonrequest.get('secret')
    #     js_code = http.request.jsonrequest.get('js_code')
    #
    #     # appid = 'wx400ff4ea33407db3'
    #     # secret = '9174cb0650b5f9ab53f475aca83b8323'
    #     # {'session_key': 'LwuaCuDNWCi1W1sNp5X0vg==', 'openid': 'o3RIy5DGHFvnqGL4VrJKtpFynDyY'}
    #
    #     grant_type = 'authorization_code'
    #     url = "https://api.weixin.qq.com/sns/jscode2session?appid=%s&secret=%s&js_code=%s&grant_type=%s" % (
    #     appid, secret, js_code, grant_type)
    #     res = requests.get(url)
    #     result = json.loads(res.content.decode('utf-8'))
    #
    #     if not result.__contains__('openid'):
    #         return {"state": "error", "msgs": result.get('errmsg')}
    #     return {"state": "success", "msgs": "查询成功", "openid": result.get('openid')}

    @http.route('/roke/get_openid', type='json', methods=['GET', 'POST', 'OPTIONS'], auth="none",
                csrf=False, cors='*')
    def new_get_openid(self):
        """
        获取openid接口
        入参：{'code': 'xxxxxx'}
        """
        _logger.info('------获取openid------')
        code = http.request.jsonrequest.get('code')
        _logger.info('入参：{}'.format(code))
        if not code:
            _logger.info('缺少入参：code')
            return {'state': 'error', 'msgs': '缺少入参：code'}
        APPID = http.request.env['ir.config_parameter'].sudo().get_param('wx.app_id')
        SECRET = http.request.env['ir.config_parameter'].sudo().get_param('wx.secret')
        url = 'https://api.weixin.qq.com/sns/jscode2session?appid={appid}&secret={secret}&js_code={code}&grant_type=authorization_code'.format(
            appid=APPID, secret=SECRET, code=code)
        try:
            res = requests.get(url)
            if res.status_code == 200:
                openid = res.json().get('openid')
                _logger.info('请求成功')
                if not openid:
                    return {'state': 'success', 'msgs': '获取失败'}

                return {'state': 'success', 'msgs': '获取成功', 'openid': openid}
            else:
                return {'state': 'error', 'msgs': '请求失败'}
        except Exception as e:
            return {'state': 'error', 'msgs': '请求失败'}

    @http.route('/roke/repair_set_level', type='json', methods=['GET', 'POST', 'OPTIONS'], auth="user",
                csrf=False, cors='*')
    def repair_set_level(self):
        """
        获取open_id
        :param kwargs:
        :return:
        """
        order_id = http.request.jsonrequest.get('order_id')
        level = http.request.jsonrequest.get('level')
        order = http.request.env['roke.mes.maintenance.order'].search([('id', '=', order_id)])
        if not order:
            return {"state": "success", "msgs": "error"}
        order.write({"level": level})
        return {"state": "success", "msgs": "提交成功"}

    @http.route('/roke/equipment/set_is_read', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False,
                cors='*')
    def set_is_read(self):
        """
        获取设备任务（维修/保养）待办数  气泡数量
        :return:
        """
        order_code = http.request.jsonrequest.get('order_code')
        _logger.info("单据设置已读{}".format(order_code))
        order = http.request.env['roke.mes.maintenance.order'].search([('code', '=', order_code)], limit=1)
        role = get_user_role(http.request.uid or http.request.session.uid)
        if role == '作业人员':
            order.write({"worker_is_read": True})
        elif role == '派工人员':
            order.write({"dispatcher_is_read": True})
        else:
            return {"state": "success", "msgs": "设置成功"}
        return {"state": "success", "msgs": "设置成功"}

    @http.route('/roke/equipment/get_scheme_by_code', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False,
                cors='*')
    def get_scheme_by_code(self):
        """
        根据设备编号查询巡检方案
        :return:
        """
        code = http.request.jsonrequest.get('code')
        plan_obj = http.request.env['roke.mes.eqpt.spot.check.plan'].search([('equipment_id.code', '=', code)])
        plan_list = []
        for plan in plan_obj:
            plan_list.append({
                "scheme_id": plan.id,
                "code": plan.code,
                "name": plan.name,
                "type": plan.type,
                "equipment_obj": {
                    "e_id": plan.equipment_id.id,
                    "e_code": plan.equipment_id.code,
                    "e_name": plan.equipment_id.name
                },
            })
        return {"state": "success", "msgs": "查询成功", "records": plan_list}

    @http.route('/roke/equipment/get_maintain_by_code', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False,
                cors='*')
    def get_maintain_by_code(self):
        """
        根据设备编号查询保养方案
        :return:
        """
        code = http.request.jsonrequest.get('code')
        e_id = http.request.env['roke.mes.equipment'].search([('code', '=', code)])
        m_plan_obj = http.request.env['roke.mes.maintenance.scheme'].search([('equipment_ids', '=', e_id.id)])
        m_plan_list = []
        for plan in m_plan_obj:
            m_plan_list.append({
                "scheme_id": plan.id,
                "code": plan.code,
                "name": plan.name,
                "last_maintenance_date": plan.last_maintenance_date,
                "equipment_obj": {
                    "e_id": e_id.id,
                    "e_code": e_id.code,
                    "e_name": e_id.name
                },
            })
        return {"state": "success", "msgs": "查询成功", "records": m_plan_list}

    @http.route('/roke/equipment/get_plan_by_scheme', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False,
                cors='*')
    def get_plan_by_scheme(self):
        """
        巡检作业-扫码获取方案&记录
        :return:
        """
        scheme_id = http.request.jsonrequest.get('scheme_id')
        plan_id = http.request.jsonrequest.get('plan_id')
        plan_obj = http.request.env['roke.mes.eqpt.spot.check.record']
        _plan = plan_obj.search([('check_plan_id', '=', scheme_id),
                                 ('check_plan_id.type', '=', 'period'),
                                 ('state', '!=', 'finish')],
                                order="create_date", limit=1)
        if plan_id:
            _plan = plan_obj.search([('id', '=', int(plan_id))], limit=1)
        _plan_dict = dict()
        if _plan:
            _plan_dict = {
                "id": _plan.id or "",
                "code": _plan.code or "",
                "equipment": {"e_id": _plan.equipment_id.id,
                              "e_code": _plan.equipment_id.code or "",
                              "e_name": _plan.equipment_id.name or "",
                              },
                "state": _plan.state,
                "state_name": get_state_name(_plan.state),
                "create_date": _plan.create_date.strftime('%Y-%m-%d') if _plan.create_date else "",
                "picture": _plan.picture,
            }
            line_list = []
            for line in _plan.item_record_ids:
                value_type = get_value_type_name(line.input_type_id.input_type)
                # 选择项
                select_item = []
                for item in line.check_item_id.select_item_ids:
                    select_item.append({
                        "value": item.value
                    })
                # 选择项
                line_list.append({
                    "id": line.id,
                    "name": line.check_item_id.name,
                    "description": line.description or "",
                    "value_type": value_type,
                    "standard_value": line.standard_value or "",
                    "select_item": select_item
                })
            _plan_dict.update({"line_list": line_list})
        else:
            scheme_obj = http.request.env['roke.mes.eqpt.spot.check.plan'].search([('id', '=', scheme_id)])
            line_list = []
            for line in scheme_obj.check_item_ids:
                value_type = get_value_type_name(line.input_type_id.input_type)
                # 选择项
                select_item = []
                for item in line.select_item_ids:
                    select_item.append({
                        "value": item.value
                    })
                # 选择项
                line_list.append({
                    "id": line.id,
                    "name": line.name,
                    "description": line.description or "",
                    "value_type": value_type,
                    "standard_value": line.standard_value or "",
                    "select_item": select_item
                })
            _plan_dict.update({"line_list": line_list})
        return {"state": "success", "msgs": "查询成功", "records": _plan_dict}

    @http.route('/roke/equipment/get_equipment', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False,
                cors='*')
    def get_equipment(self):
        """
        获取设备
        :param: name：设备名称，模糊搜索
        :param: equipment_id：设备ID，扫码查询
        :return:
        """
        name = http.request.jsonrequest.get('name', False)
        equipment_id = http.request.jsonrequest.get('equipment_id', False)
        if equipment_id:
            domain = [("id", "=", int(equipment_id))]
        elif name:
            domain = [("name", "ilike", name)]
        else:
            return {
                "state": "error",
                "msgs": "参数不全",
            }
        result = []
        _logger.info(domain)
        equipments = http.request.env['roke.mes.equipment'].search(domain)
        if not equipments:
            return {
                "state": "error",
                "msgs": "未查询到设备！",
            }
        for equipment in equipments:
            result.append({
                "id": equipment.id,
                "code": equipment.code or "",
                "name": equipment.name or "",
                "category": equipment.category_id.name or "",
                "work_center": equipment.work_center_id.name or "",
                "manufacturer": equipment.manufacturer or "",
                "specification": equipment.specification or "",
                "in_repair": "是" if equipment.in_repair else "否",
                "e_state": equipment.e_state,
            })
        return {
            "state": "success",
            "msgs": "获取成功",
            "equipments": result
        }

    def _get_repair_record_list(self, filter_param):
        """
        获取报修记录单据列表
        :return:
        """
        _logger.info('--方法获取的参数--')
        _logger.info(filter_param)
        order_type = filter_param.get("order_type")
        if not order_type or order_type not in ("repair", "maintain"):
            return {"state": "error", "msgs": "单据类型错误，请联系后台相关人员"}
        domain = [("type", "=", order_type)]
        if filter_param.get("code"):
            domain.append(("code", "ilike", filter_param.get("code")))
        if filter_param.get("equipment_id"):
            domain.append(("equipment_id", "=", int(filter_param.get("equipment_id"))))
        if filter_param.get("report_time"):
            domain.append(("report_time", "=", filter_param.get("report_time")))
        if filter_param.get("e_code"):
            domain.append(("equipment_code", "=", filter_param.get("e_code")))
        limit = http.request.jsonrequest.get('limit', 50)
        page_number = http.request.jsonrequest.get('page_number', 1)
        result = []
        orders = http.request.env['roke.mes.maintenance.order'].search(domain, order='report_time desc')
        page_orders = orders[(page_number - 1) * limit: (page_number - 1) * limit + limit]
        _logger.info(page_orders)
        for order in page_orders:
            r = get_repair_origin(order.repair_origin)
            result.append({
                "id": order.id,
                "code": order.code or "",
                "equipment": {"e_id": order.equipment_id.id, "e_code": order.equipment_id.code or "",
                              "e_name": order.equipment_id.name or "", "e_location": order.e_location or ""},
                "assign_user": order.report_user_id.name or "",
                "assign_phone": order.report_user_id.phone,
                "state": http_tool.selection_to_dict("roke.mes.maintenance.order", "state")[
                    order.state] if order.state else "",
                "priority": http_tool.selection_to_dict("roke.mes.maintenance.order", "priority")[
                    order.priority] if order.priority else "",
                "deadline": order.deadline.strftime('%Y-%m-%d') if order.deadline else "",
                "create_date": order.create_date.strftime('%Y-%m-%d') if order.create_date else "",
                "repair_origin": r,
                "fault_id": order.fault_id.id,
                "fault_name": order.fault_id.name,
                "report_user_id": order.report_user_id.id,
                "report_user_name": order.report_user_id.name,
            })
        return_data = {
            "state": "success",
            "msgs": "获取成功",
            "records": result,
            "total": len(orders),
            "page_total": math.ceil(len(orders) / limit),
            "limit": limit,
            "page_number": page_number
        }
        _logger.info('===返回的数据===')
        _logger.info(return_data)
        return return_data

    @http.route('/roke/equipment/search', type='json',methods=['POST', 'OPTIONS'], auth="none", csrf=False,
                cors='*')
    def search_equipment(self):
        """
        根据 plant_id   category_id 查询设备（JSON POST）
        请求示例:
            {
                "plant_name": ,
                "category_name":
            }
        """
        # 获取请求数据
        data = http.request.jsonrequest
        plant_name = data.get('plant_name')
        category_name = data.get('category_name')

        data_acquisition_code = data.get('data_acquisition_code')

        domain = []

        if data_acquisition_code:
            domain.append(('data_acquisition_code', 'in', data_acquisition_code))

        # 构建查询条件
        if plant_name:
            domain.append(('plant_id.name', '=', plant_name))

        if category_name:
            domain.append(('category_id.name', '=', category_name))

        if not domain:
            return {
                "state": "error",
                "msgs": "参数不全;车间和 设备类别不能同时为空",
                "data": []
            }

        # 查询设备
        equipments = http.request.env['roke.mes.equipment'].sudo().search(domain)

        # 构造响应数据
        equipment_list = [{
            'id': eq.id,
            'device_name': eq.name,
            'device_code': eq.code,
            'data_acquisition_code':eq.data_acquisition_code,
            'category': eq.category_id.name if eq.category_id else '',
            'plant_name': eq.plant_id.name if eq.plant_id else '',
        } for eq in equipments]

        return {
            'status': 'success',
            'code': 200,
            'data': equipment_list
        }
    @http.route('/roke/equipment/info', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False,
                cors='*')
    def get_equipment_info(self):
        """获取设备基础信息--物联网灯"""
        equipment_id = http.request.jsonrequest.get('equipment_id', False)
        if not equipment_id:
            return {"state": "error","msgs": "缺少必传参数: equipment_id"}
        equipment = http.request.env['roke.mes.equipment'].browse(equipment_id)
        if not equipment:
            return {"state": "error", "msgs": "未查询到设备信息"}
        return {
            "state": "success",
            "msgs": "获取成功",
            "equipment": {
                "id": equipment.id,
                "name": equipment.name,
                "code": equipment.code,
                "category": equipment.category_id.name,
                "specification": equipment.specification,
                "e_state": equipment.e_state,
                "warranty_date": (equipment.warranty_date + datetime.timedelta(hours=8)).strftime('%Y-%m-%d') if equipment.warranty_date else "",
                "repair_record_list": self.get_equipment_repair_list(equipment_id, "repair"),
                "maintain_record_list": self.get_equipment_repair_list(equipment_id, "maintain"),
                "check_record_list": self.get_equipment_check_list(equipment_id),
                "change_record_list": self.get_equipment_change_list(equipment_id),
            }
        }

    @http.route('/roke/create/equipment/change_record', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False,
                cors='*')
    def create_equipment_change_record_record(self):
        """创建设备更换件记录--物联网灯"""
        equipment_id = http.request.jsonrequest.get('equipment_id', False)
        name = http.request.jsonrequest.get('name', False)
        new_name = http.request.jsonrequest.get('new_name', False)
        if not all([equipment_id, name, new_name]):
            return {"state": "error","msgs": "缺少必传参数"}
        http.request.env['roke.mes.equipment.change.record'].create({
            "e_id": equipment_id,
            "name": name,
            "new_name": new_name,
            "record_date": fields.Datetime.now(),
            "change_user_id": http.request.env.user.id,
        })
        return {"state": "success", "msgs": "创建成功"}
    
    @http.route('/roke/equipment/change_list', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False,
                cors='*')
    def get_equipment_change_record_list(self):
        """获取设备更换件记录--物联网灯"""
        equipment_id = http.request.jsonrequest.get('equipment_id', False)
        if not equipment_id:
            return {"state": "error","msgs": "缺少必传参数: equipment_id"}
        page_size = int(http.request.jsonrequest.get('page_size', 10))
        page_no = int(http.request.jsonrequest.get('page_no', 1))
        offset = (page_no - 1) * page_size
        record_list = self.get_equipment_change_list(equipment_id, page_size, offset)
        return {"state": "success", "msgs": "获取成功", "records": record_list}

    @http.route('/roke/equipment/check_list', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False,
                cors='*')
    def get_equipment_check_record_list(self):
        """获取设备点检记录--物联网灯"""
        equipment_id = http.request.jsonrequest.get('equipment_id', False)
        if not equipment_id:
            return {"state": "error","msgs": "缺少必传参数: equipment_id"}
        page_size = int(http.request.jsonrequest.get('page_size', 10))
        page_no = int(http.request.jsonrequest.get('page_no', 1))
        offset = (page_no - 1) * page_size
        record_list = self.get_equipment_check_list(equipment_id, page_size, offset)
        return {"state": "success", "msgs": "获取成功", "records": record_list}
    
    @http.route('/roke/equipment/maintenance_list', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False,
                cors='*')
    def get_equipment_maintenance_list(self):
        """获取设备维保记录--物联网灯"""
        equipment_id = http.request.jsonrequest.get('equipment_id', False)
        if not equipment_id:
            return {"state": "error","msgs": "缺少必传参数: equipment_id"}
        type = http.request.jsonrequest.get('type', "repair")
        page_size = int(http.request.jsonrequest.get('page_size', 10))
        page_no = int(http.request.jsonrequest.get('page_no', 1))
        offset = (page_no - 1) * page_size
        record_list = self.get_equipment_repair_list(equipment_id, type, page_size, offset)
        return {"state": "success", "msgs": "获取成功", "records": record_list}
    
    def get_equipment_change_list(self, equipment_id, limit=2, offset=0):
        """获取单个设备更换件记录"""
        record_list = []
        record_ids = http.request.env['roke.mes.equipment.change.record'].search([("e_id", "=", equipment_id)], 
                                                                                limit=limit, offset=offset, order="record_date desc")
        for item in record_ids:
            record_list.append({
                "id": item.id,
                "code": item.code,
                "name": item.name,
                "new_name": item.new_name,
                "record_date": (item.record_date + datetime.timedelta(hours=8)).strftime('%Y-%m-%d') if item.record_date else "",
                "change_user_id": item.change_user_id.id,
                "change_user_name": item.change_user_id.name if item.change_user_id else "",
            })
        return record_list
    
    def get_equipment_check_list(self, equipment_id, limit=2, offset=0):
        """获取单个设备巡检记录"""
        record_list = []
        record_ids = http.request.env['roke.mes.eqpt.spot.check.record'].search([("equipment_id", "=", equipment_id)], 
                                                                                limit=limit, offset=offset, order="create_date desc")
        for item in record_ids:
            record_list.append({
                "id": item.id,
                "code": item.code,
                "check_plan_id": item.check_plan_id.id,
                "check_plan_name": item.check_plan_id.name if item.check_plan_id else "",
                "assign_user_name": item.assign_user_ids[0].name if item.assign_user_ids else "",
                "finish_time": (item.finish_time + datetime.timedelta(hours=8)).strftime('%Y-%m-%d') if item.finish_time else "",
                "state": http_tool.get_selection_field_values(item, "state"),
                "description": item.description,
                "finish_user_id": item.finish_user_id.id,
                "finish_user_name": item.finish_user_id.name if item.finish_user_id else "",
                "item_record_names": ",".join([item_id.check_item_id.name for item_id in item.item_record_ids]) if item.item_record_ids else "",
                "item_record_list": [
                    {
                        "id": item_id.id,
                        "check_item_id": item_id.check_item_id.id,
                        "check_item_name": item_id.check_item_id.name,
                        "check_value": item_id.check_value,
                        "result": item_id.result,
                        "description": item_id.description,
                    } for item_id in item.item_record_ids
                ]
            })
        return record_list
    
    def get_equipment_repair_list(self, equipment_id, type="repair", limit=2, offset=0):
        """获取单个设备报修记录"""
        record_list = []
        if type == "repair":
            repair_ids = http.request.env['roke.mes.maintenance.order'].search( [("equipment_id", "=", equipment_id), ("type", "=", type)],
                limit=limit, offset=offset, order="report_time desc")
            for item in repair_ids:
                record_list.append({
                    "id": item.id,
                    "report_user_id": item.report_user_id.id,
                    "report_user_name": item.report_user_id.name,
                    "repair_user_id": item.repair_user_id.id,
                    "repair_user_name": item.repair_user_id.name,
                    "report_time": (item.report_time + datetime.timedelta(hours=8)).strftime('%Y-%m-%d') if item.report_time else "",
                    "fault_description": item.fault_description,
                    "state": http_tool.get_selection_field_values(item, "state"),
                    "maintenance_scheme": item.maintenance_scheme_id.name if item.maintenance_scheme_id else "",
                    "finish_time": (item.finish_time + datetime.timedelta(hours=8)).strftime('%Y-%m-%d %H:%M:%S') if item.finish_time else "",
                    "equipment_name": item.equipment_id.name,
                    "priority": item.priority,
                    "last_maintenance_date": item.last_maintenance_date.strftime('%Y-%m-%d') if item.last_maintenance_date else "",
                    "use_time": item.use_time,
                    "item_list":[]
                })

        elif type == "maintain":
            repair_ids = http.request.env['roke.mes.maintenance.order'].search(
                [("equipment_id", "=", equipment_id), ("type", "=", type)],
                limit=limit, offset=offset, order="finish_time desc")
            for item in repair_ids:
                vals = {
                    "id": item.id,
                    "report_user_id": item.report_user_id.id,
                    "report_user_name": item.report_user_id.name,
                    "repair_user_id": item.repair_user_id.id,
                    "repair_user_name": item.repair_user_id.name,
                    "report_time": (item.report_time + datetime.timedelta(hours=8)).strftime('%Y-%m-%d') if item.report_time else "",
                    "fault_description": item.fault_description,
                    "state": http_tool.get_selection_field_values(item, "state"),
                    "maintenance_scheme": item.maintenance_scheme_id.name if item.maintenance_scheme_id else "",
                    "finish_time": (item.create_date + datetime.timedelta(hours=8)).strftime('%Y-%m-%d') if item.create_date else "",
                    "equipment_name": item.equipment_id.name,
                    "priority": item.priority,
                    "last_maintenance_date": (item.last_maintenance_date+ datetime.timedelta(hours=8)).strftime('%Y-%m-%d') if item.last_maintenance_date else "",
                    "use_time": item.use_time,
                }
                item_list = []
                for item_line in item.item_ids:
                    item_list.append({
                        "item_id": item_line.item_id.id,
                        "item_name":item_line.item_id.name,
                        "item_code":item_line.item_id.code,
                        "execute_user_id": item_line.execute_user_id.id,
                        "execute_user_name": item_line.execute_user_id.name,
                        "execute_time": (item_line.execute_time+ datetime.timedelta(hours=8)).strftime('%Y-%m-%d') if item_line.execute_time else "",
                        "execute_description": item_line.description,
                    })
                vals["item_list"] = item_list
                record_list.append(vals)

        return record_list

    @http.route('/roke/mes/create_repair',type='json', methods=['POST', 'OPTIONS'], auth="user",
                csrf=False, cors='*')
    def create_repair(self):
        """
        创建维修记录
        :return:
        """
        try:
            equipment_id = http.request.jsonrequest.get('equipment_id', False)
            priority = http.request.jsonrequest.get('priority', False)
            repair_user_id = http.request.jsonrequest.get('repair_user_id', False)
            deadline = http.request.jsonrequest.get('deadline', False)
            fault_description = http.request.jsonrequest.get('fault_description', '')
            user_id = http.request.env.user
            request = http.request.env['roke.mes.maintenance.order'].sudo()
            vals = {
                "code": http.request.env['ir.sequence'].sudo().search([('code', '=', 'roke.mes.repair.code')],
                                                                      limit=1)._next(),
                "state":"wait",
                "type": 'repair',
                "user_id": (user_id and user_id.id) or 2,
                "equipment_id": int(equipment_id),
                "priority": priority,
                "repair_user_id": int(repair_user_id),
                "report_user_id":int(user_id and user_id.id) or 2,
                "deadline": deadline,
                "report_time": fields.Datetime.now() ,
                "fault_description": fault_description,
            }
            res = request.create(vals)
            return {"state": "success", "msgs": "创建成功", 'task_id': res.id}
        except Exception as e:
            buff = StringIO()
            traceback.print_exc(file=buff)
            trace_e = buff.getvalue()
            return {"state": "error", "msgs": str(trace_e), "task_id": -1}

    @http.route('/roke/mes/create_maintenance', type='json', methods=['POST', 'OPTIONS'], auth="user",
                csrf=False, cors='*')
    def create_maintenance(self):
        """
        创建保养任务
        :return:
        """
        try:
            equipment_id = http.request.jsonrequest.get('equipment_id', False)
            maintenance_scheme = http.request.jsonrequest.get('maintenance_scheme', False)
            priority = http.request.jsonrequest.get('priority', False)
            repair_user_id = http.request.jsonrequest.get('repair_user_id', False)
            items_list = http.request.jsonrequest.get('items_list', False)
            user_id = http.request.env.user
            request = http.request.env['roke.mes.maintenance.order'].sudo()
            vals = {
                "code": http.request.env['ir.sequence'].sudo().search([('code','=','roke.mes.repair.code')],limit=1)._next(),
                "type":'maintain',
                "user_id":(user_id and user_id.id) or 2,
                "equipment_id": int(equipment_id),
                "maintenance_scheme_id": int(maintenance_scheme),
                "priority": priority,
                "repair_user_id": int(repair_user_id),
                "item_ids":[]
            }

            for item in items_list:
                vals['item_ids'].append((0, 0, {
                    "item_id": int(item.get('item_id')),
                    "description": item.get('description'),
                    "execute_user_id":(user_id and user_id.id) or 2,
                    "execute_time": item.get('execute_time'),
                }))
            res = request.create(vals)
            return {"state": "success", "msgs": "创建成功",'task_id':res.id}
        except Exception as e:
            buff = StringIO()
            traceback.print_exc(file=buff)
            trace_e = buff.getvalue()
            return {"state": "error", "msgs": str(trace_e),"task_id":-1}

    @http.route('/maintenance/scheme/items', type='json', methods=['POST', 'OPTIONS'], auth="user",
                csrf=False, cors='*')
    def get_maintenance_scheme_items(self):
        """
        获取指定保养方案的保养项目
        :param scheme_id: 保养方案的 ID
        :return: 保养项目的列表
        """
        #
        scheme_id = http.request.jsonrequest.get('scheme_id', False)
        scheme = http.request.env['roke.mes.maintenance.scheme'].browse(scheme_id)

        # 检查记录是否存在
        if not scheme.exists():
            return {'state': 'error', 'msg': '保养方案不存在', 'results': []}

        # 获取保养项目
        items = scheme.item_ids.read(['id', 'name', 'code'])

        return {'state': 'success', 'results': items, 'msg': ''}

    @http.route('/roke/create/check_record', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False,
                cors='*')
    def create_check_record(self):
        """创建设备点检单--物联网灯"""
        equipment_id = http.request.jsonrequest.get('equipment_id', False)
        check_plan_id = http.request.jsonrequest.get('check_plan_id', False)
        finish_user_id = http.request.jsonrequest.get('finish_user_id', False)
        item_list = http.request.jsonrequest.get('item_list', [])
        if not all([equipment_id, check_plan_id, finish_user_id, item_list]):
            return {"state": "error","msgs": "缺少必传参数"}
        check_id = http.request.env['roke.mes.eqpt.spot.check.record'].create({
            "equipment_id": equipment_id,
            "check_plan_id": check_plan_id,
            "finish_user_id": finish_user_id,
            "is_api": True,
            "item_record_ids": [(0, 0, {
                "check_item_id": item.get("check_item_id"),
                "check_value": item.get("check_value"),
                "result": item.get("result"),
            }) for item in item_list]
        })
        return {"state": "success", "msgs": "创建成功", "records": check_id.id}

    @http.route('/roke/get/check_plan_list', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False,
                cors='*')
    def get_check_plan_list(self):
        """获取设备点检方案列表--物联网灯"""
        domain = []
        equipment_id = http.request.jsonrequest.get('equipment_id', False)
        if equipment_id:
            domain.append(("equipment_id", "=", equipment_id))
        check_ids = http.request.env['roke.mes.eqpt.spot.check.plan'].search(domain)
        check_list = []
        for check_id in check_ids:
            check_list.append({
                "id": check_id.id,
                "name": check_id.name,
                "code": check_id.code,
                "equipment_id": check_id.equipment_id.id,
                "equipment_name":check_id.equipment_id.name,
                "specification":check_id.equipment_id.specification,
                "warranty_date": fields.Date.to_string(check_id.equipment_id.warranty_date),
                "check_item_list": [
                    {
                        "id": item.id,
                        "name": item.name,
                    } for item in check_id.check_item_ids
                ]
            })
        return {"state": "success", "msgs": "获取成功", "records": check_list}

