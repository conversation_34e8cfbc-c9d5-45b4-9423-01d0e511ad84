<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--技能等级-->
    <!--search-->
    <record id="view_roke_skill_level_search" model="ir.ui.view">
        <field name="name">roke.skill.level.search</field>
        <field name="model">roke.skill.level</field>
        <field name="arch" type="xml">
            <search string="技能等级">
                <field name="name"/>
                <field name="level"/>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_skill_level_tree" model="ir.ui.view">
        <field name="name">roke.skill.level.tree</field>
        <field name="model">roke.skill.level</field>
        <field name="arch" type="xml">
            <tree string="技能等级">
                <field name="name"/>
                <field name="level"/>
                <field name="note" optional="show"/>
                <field name="create_uid" string="创建人"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_skill_level_form" model="ir.ui.view">
        <field name="name">roke.skill.level.form</field>
        <field name="model">roke.skill.level</field>
        <field name="arch" type="xml">
            <form string="技能等级">
                <sheet>
                    <group col="4">
                        <group>
                            <field name="name"/>
                        </group>
                        <group>
                            <field name="level"/>
                        </group>
                        <group>
                            <field name="note"/>
                        </group>
                        <group>
                            <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>
    <!--action-->
    <record id="view_roke_skill_level_action" model="ir.actions.act_window">
        <field name="name">技能等级</field>
        <field name="res_model">roke.skill.level</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{}</field>
        <field name="form_view_id" ref="view_roke_skill_level_form"/>
        <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
            点击左上角“创建”按钮新增一个技能等级。
          </p>
        </field>
    </record>

</odoo>
