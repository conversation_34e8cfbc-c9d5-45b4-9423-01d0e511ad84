# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.
{
    'name': '融科MES 设备管理',
    'version': '1.0',
    'category': 'mes',
    'depends': ['roke_mes_base'],
    'author': 'www.rokedata.com',
    'website': 'http://www.rokedata.com',
    'description': """

基于13版本的融科MES产品 设备管理
========================================================================

包含:
-------------------
    * 设备管理:
        * 设备
        * 工作中心设备
        * 工位设备
    * 设备维保:
        * 报修/维修
        * 点检
        * 保养
    """,
    'data': [
        'data/data.xml',
        'data/sequence_data.xml',
        'data/cron_data.xml',
        'data/work_special_work_type_data.xml',
        'data/mobile_data.xml',
        'security/ir.model.access.csv',
        'security/security_group.xml',
        'report/qr_image_report.xml',
        'views/roke_mes_equipment_views.xml',
        'views/roke_spare_part_views.xml',
        'views/roke_mes_equipment_check_item_views.xml',
        'views/roke_mes_eqpt_spot_plan_views.xml',
        'views/roke_mes_eqpt_spot_record_views.xml',
        'views/roke_mes_maintenance_item_views.xml',
        'views/roke_mes_maintenance_views.xml',
        'views/roke_mes_maintenance_order_views.xml',
        'views/roke_mes_repair_order_views.xml',
        'views/roke_mes_equipment_periodic_test_views.xml',
        'views/roke_mes_special_work_views.xml',
        'views/roke_mes_equipemnt_record_views.xml',
        'views/roke_mes_equipment_change_record_views.xml',
        'views/roke_work_center_views_inherit.xml',
        'views/roke_mes_equipment_acceptance_views.xml',
        'views/roke_mes_equipment_input_views.xml',
        'views/roke_equipment_holiday_arrangement_view.xml',
        'wizard/execute_maintenance_order_views.xml',
        'wizard/execute_maintenance_item_views.xml',
        'wizard/equipment_repair_request_views.xml',
        'wizard/maintain_create_special_work_views.xml',
        'wizard/execute_check_item_views.xml',
        'wizard/roke_mes_maintenance_order_wizard_views.xml',
        'views/spot_check_plan.xml',
        'views/maintenance_check_plan.xml',
        'views/menus.xml',
        'views/update_menus.xml'

    ],
    'demo': [
        'data/spare_part_demo_data.xml',
    ],
    'application': True,
    'installable': True,
    'auto_install': False,
    'post_init_hook': 'predefined_menu_permissions',
}

