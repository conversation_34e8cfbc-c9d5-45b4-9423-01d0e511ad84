<?xml version="1.0" encoding="utf-8"?>
<odoo>
        <!-- ir.mail.server -->
        <record model="ir.ui.view" id="ir_mail_server_form">
            <field name="model">ir.mail_server</field>
            <field name="arch" type="xml">
                <form string="Outgoing Mail Servers">
                  <sheet>
                    <group col="4">
                        <field name="name"/>
                        <field name="sequence"/>
                        <field name="active" widget="boolean_toggle"/>
                    </group>
                    <group col="4" string="Connection Information">
                        <field name="smtp_host"/>
                        <field name="smtp_port" options="{'format': false}"/>
                        <field name="smtp_debug" groups="base.group_no_one"/>
                     </group>
                     <group string="Security and Authentication" colspan="4">
                        <field name="smtp_encryption"/>
                        <field name="smtp_user"/>
                        <field name="smtp_pass" password="True"/>
                        <button name="test_smtp_connection" type="object" string="Test Connection" icon="fa-television"/>
                    </group>
                  </sheet>  
                </form>
            </field>
        </record>

        <record model="ir.ui.view" id="ir_mail_server_list">
            <field name="model">ir.mail_server</field>
            <field name="arch" type="xml">
                <tree string="Outgoing Mail Servers">
                    <field name="sequence"/>
                    <field name="name"/>
                    <field name="smtp_host"/>
                    <field name="smtp_user"/>
                    <field name="smtp_encryption"/>
                </tree>
            </field>
        </record>

        <record id="view_ir_mail_server_search" model="ir.ui.view">
            <field name="model">ir.mail_server</field>
            <field name="arch" type="xml">
                <search string="Outgoing Mail Servers">
                    <field name="name"
                        filter_domain="['|', '|', ('name','ilike',self), ('smtp_host','ilike',self), ('smtp_user','ilike',self)]"
                        string="Outgoing Mail Server"/>
                    <field name="smtp_encryption"/>
                    <separator/>
                    <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                </search>
            </field>
        </record>

        <record model="ir.actions.act_window" id="action_ir_mail_server_list">
            <field name="name">Outgoing Mail Servers</field>
            <field name="res_model">ir.mail_server</field>
            <field name="view_mode">tree,form</field>
            <field name="view_id" ref="ir_mail_server_list" />
            <field name="search_view_id" ref="view_ir_mail_server_search"/>
        </record>

        <menuitem id="menu_mail_servers" parent="menu_email" action="action_ir_mail_server_list" sequence="5" groups="base.group_no_one"/>
</odoo>
