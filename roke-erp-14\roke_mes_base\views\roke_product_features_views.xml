<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--产品特征字典-->
    <!--search-->
    <record id="view_roke_product_features_search" model="ir.ui.view">
        <field name="name">roke.product.features.search</field>
        <field name="model">roke.product.features</field>
        <field name="arch" type="xml">
            <search string="产品特征字典">
                <field name="name"/>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_product_features_tree" model="ir.ui.view">
        <field name="name">roke.product.features.tree</field>
        <field name="model">roke.product.features</field>
        <field name="arch" type="xml">
            <tree string="产品特征字典">
                <field name="name"/>
                <field name="note" optional="show"/>
                <field name="create_uid" string="创建人"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_product_features_form" model="ir.ui.view">
        <field name="name">roke.product.features.form</field>
        <field name="model">roke.product.features</field>
        <field name="arch" type="xml">
            <form string="产品特征字典">
                <sheet>
                    <group id="g1">
                        <group>
                            <group>
                                <field name="name"/>
                            </group>
                            <group>
                                <field name="note" widget="char"/>
                            </group>
                        </group>
                        <group>
                            <group>
                                <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}"/>
                            </group>
                            <group></group>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>
    <!--action-->
    <record id="view_roke_product_features_action" model="ir.actions.act_window">
        <field name="name">产品特征字典</field>
        <field name="res_model">roke.product.features</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{}</field>
        <field name="form_view_id" ref="view_roke_product_features_form"/>
        <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
            点击左上角“创建”按钮新增一个产品特征字典。
          </p>
        </field>
    </record>

</odoo>