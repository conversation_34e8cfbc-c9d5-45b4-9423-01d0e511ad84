<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="routing_settings_assets_backend" name="roke_workstation_api assets" inherit_id="web.assets_backend">
        <xpath expr="." position="inside">
            <link rel="stylesheet" type="text/scss" href="/roke_workstation_api/static/src/components/routing_settings.scss"/>
            <link rel="stylesheet" type="text/scss" href="/roke_workstation_api/static/src/components/product_list.scss"/>
            <link rel="stylesheet" type="text/scss" href="/roke_workstation_api/static/src/components/product_info.scss"/>
            <link rel="stylesheet" type="text/scss" href="/roke_workstation_api/static/src/components/process_list.scss"/>
            <link rel="stylesheet" type="text/scss" href="/roke_workstation_api/static/src/components/process_settings.scss"/>
            
            <script type="application/javascript" src="/roke_workstation_api/static/src/lib/sortable.min.js" />

            <script type="text/javascript" src="/roke_workstation_api/static/src/js/roke_routing_settings.js"/>
            <script type="text/javascript" src="/roke_workstation_api/static/src/components/routing_settings.js"/>
            <script type="text/javascript" src="/roke_workstation_api/static/src/components/product_list.js"/>
            <script type="text/javascript" src="/roke_workstation_api/static/src/components/product_info.js"/>
            <script type="text/javascript" src="/roke_workstation_api/static/src/components/process_list.js"/>
            <script type="text/javascript" src="/roke_workstation_api/static/src/components/process_settings.js"/>

            <script type="text/javascript" src="/roke_workstation_api/static/src/js/roke_routing_config.js"/>
            <script type="text/javascript" src="/roke_workstation_api/static/src/js/roke_equipment_status.js"/>
            <script type="text/javascript" src="/roke_workstation_api/static/src/js/roke_ws_state_list.js"/>
            <script type="text/javascript" src="/roke_workstation_api/static/src/js/roke_ws_state_kanban.js"/>
            <script type="text/javascript" src="/roke_workstation_api/static/src/js/abnormal_alarm.js"/>
            <script type="text/javascript" src="/roke_workstation_api/static/src/js/work_order_staticfy.js"/>
            <script type="text/javascript" src="/roke_workstation_api/static/src/js/equipment_staticfy.js"/>
            <script type="text/javascript" src="/roke_workstation_api/static/src/js/energy_staticfy.js"/>


        </xpath>
   </template>
</odoo>
