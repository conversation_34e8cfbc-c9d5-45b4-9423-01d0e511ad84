# 备件使用记录模型功能总结

## 概述
新增了`roke.spare.part.usage.record`模型，用于跟踪备件的使用情况，包括更换时间、到期时间计算、使用状态管理等功能。

## 模型字段详细说明

### 基本信息字段
1. **name** - 记录编号（自动生成，格式：SPU000001）
2. **equipment_id** - 针对设备（必填，关联roke.mes.equipment）
3. **spare_part_id** - 备件（必填，关联roke.spare.part）
4. **removed_part_id** - 拆下备件（可选，关联roke.spare.part）
5. **replacement_time** - 更换时间（必填，默认当前时间）
6. **operator_id** - 操作人员（默认当前用户）
7. **maintenance_order_id** - 关联维修单（可选）

### 状态管理字段
8. **state** - 状态（使用中/已到期/已更换/已报废）
9. **notes** - 备注信息

### 计算字段
10. **expiry_time** - 备件寿命到期时间（自动计算）
11. **remaining_days** - 剩余天数（自动计算）
12. **usage_days** - 已使用天数（自动计算）

### 关联显示字段
13. **spare_part_code** - 备件编号（关联显示）
14. **spare_part_name** - 备件名称（关联显示）
15. **spare_part_manufacturer** - 备件厂家（关联显示）
16. **theoretical_life** - 理论寿命（关联显示）
17. **life_unit** - 寿命单位（关联显示）
18. **equipment_code** - 设备编号（关联显示）
19. **equipment_name** - 设备名称（关联显示）

## 核心功能

### 1. 自动到期时间计算
根据备件的理论寿命和寿命单位，自动计算备件的到期时间：
- **年**：使用relativedelta按年计算
- **月**：使用relativedelta按月计算
- **日**：使用timedelta按天计算

### 2. 状态管理
- **使用中（active）**：备件正在使用中
- **已到期（expired）**：备件已超过理论寿命
- **已更换（replaced）**：备件已被新备件替换
- **已报废（scrapped）**：备件已报废处理

### 3. 自动到期检查
- 每日定时任务自动检查备件是否到期
- 自动将到期的备件状态更新为"已到期"
- 可扩展添加到期通知功能

### 4. 统计计算
- **剩余天数**：距离到期的天数
- **已使用天数**：从更换到现在的天数
- **使用记录数**：备件的总使用次数

## 视图功能

### 1. 列表视图
- 支持颜色标识：到期记录显示红色，即将到期显示黄色
- 显示关键信息：设备、备件、时间、状态等
- 支持多种排序和筛选

### 2. 表单视图
- 分组显示：基本信息、寿命信息、备件详情
- 状态栏操作：快速更改记录状态
- 邮件线程：支持消息跟踪和活动管理

### 3. 看板视图
- 按状态分组显示
- 卡片式展示关键信息
- 颜色标识到期状态

### 4. 搜索和筛选
- 按设备、备件、操作人员筛选
- 按状态筛选（使用中、已到期、已更换等）
- 按时间范围筛选（本月、本年等）
- 即将到期筛选（30天内到期）

## 集成功能

### 1. 与备件模型集成
- 备件表单中显示使用记录统计按钮
- 备件表单中添加使用记录页面
- 支持从备件直接查看所有使用记录

### 2. 邮件线程集成
- 继承mail.thread和mail.activity.mixin
- 支持消息跟踪和活动管理
- 状态变更自动记录

### 3. 序列号自动生成
- 自动生成唯一记录编号
- 格式：SPU + 6位数字（如SPU000001）

## 文件结构

### 模型文件
- `models/roke_spare_part_usage_record.py` - 主要模型定义

### 视图文件
- `views/roke_spare_part_usage_record_views.xml` - 所有视图定义

### 数据文件
- `data/spare_part_usage_record_sequence.xml` - 序列号配置
- `data/spare_part_usage_record_cron.xml` - 定时任务配置
- `data/spare_part_usage_record_demo_data.xml` - 演示数据

### 权限文件
- `security/ir.model.access.csv` - 访问权限配置

## 使用场景

### 1. 备件更换记录
- 记录设备维修时的备件更换情况
- 跟踪拆下的旧备件和安装的新备件
- 记录操作人员和更换时间

### 2. 备件寿命管理
- 自动计算备件到期时间
- 提前预警即将到期的备件
- 统计备件的实际使用寿命

### 3. 维护计划制定
- 基于备件使用记录制定维护计划
- 分析备件更换频率和模式
- 优化备件库存管理

### 4. 成本分析
- 统计备件使用成本
- 分析设备维护成本构成
- 评估备件供应商质量

## 扩展建议

### 1. 通知功能
- 备件即将到期邮件通知
- 备件已到期短信提醒
- 移动端推送通知

### 2. 报表功能
- 备件使用统计报表
- 设备维护成本分析
- 备件寿命分析报告

### 3. 预测功能
- 基于历史数据预测备件需求
- 智能库存补货建议
- 维护计划自动生成

### 4. 移动端支持
- 移动端扫码录入
- 现场快速记录
- 离线数据同步

## 注意事项

1. **数据完整性**：确保设备和备件数据的准确性
2. **时间计算**：注意时区设置对时间计算的影响
3. **权限控制**：合理设置用户访问权限
4. **性能优化**：大量数据时考虑索引优化
5. **备份策略**：重要的使用记录数据需要定期备份

这个备件使用记录系统提供了完整的备件生命周期管理功能，从安装到到期的全过程跟踪，有助于提高设备维护效率和降低维护成本。
