# API Documentation for `/roke/spare_part/usage_records`

## 基本信息
- **接口名称**: 根据备件ID查询备件使用记录
- **接口描述**: 根据备件ID查询备件使用记录，返回针对设备、拆下备件、更换时间、备件寿命到期时间等信息，支持分页
- **请求方式**: POST
- **认证方式**: 用户认证 (auth="user")
- **数据格式**: JSON
- **CORS支持**: 是

## 请求参数

### 请求头
```
Content-Type: application/json
Authorization: Bearer <token>
```

### 请求体参数
| 参数名 | 类型 | 必填 | 默认值 | 描述 |
|-------|------|-----|-------|------|
| spare_part_id | Integer | 是 | - | 备件ID |
| page | Integer | 否 | 1 | 页码，从1开始 |
| page_size | Integer | 否 | 10 | 每页数量，最大100 |

### 请求示例

#### 查询备件使用记录（第一页）
```json
{
  "spare_part_id": 1,
  "page": 1,
  "page_size": 10
}
```

#### 查询第二页数据
```json
{
  "spare_part_id": 1,
  "page": 2,
  "page_size": 20
}
```

## 响应数据

### 成功响应
```json
{
  "state": "success",
  "msgs": "获取成功",
  "data": {
    "spare_part_info": {
      "id": 1,
      "name": "深沟球轴承",
      "code": "SP000001",
      "model": "6205-2RS",
      "manufacturer": "SKF"
    },
    "usage_records": [
      {
        "id": 1,
        "equipment_id": 5,
        "equipment_name": "生产设备A",
        "equipment_code": "EQ001",
        "removed_part_id": 2,
        "removed_part_name": "旧轴承",
        "removed_part_code": "SP000002",
        "replacement_time": "2023-06-01 10:30:00",
        "expiry_time": "2025-06-01 10:30:00",
        "remaining_days": 365,
        "usage_days": 30,
        "maintenance_order_id": 10,
        "maintenance_order_code": "MO000010",
        "spare_part_name": "深沟球轴承",
        "spare_part_code": "SP000001"
      },
      {
        "id": 2,
        "equipment_id": 6,
        "equipment_name": "生产设备B",
        "equipment_code": "EQ002",
        "removed_part_id": null,
        "removed_part_name": "",
        "removed_part_code": "",
        "replacement_time": "2023-05-15 14:20:00",
        "expiry_time": "2025-05-15 14:20:00",
        "remaining_days": 348,
        "usage_days": 47,
        "maintenance_order_id": null,
        "maintenance_order_code": "",
        "spare_part_name": "深沟球轴承",
        "spare_part_code": "SP000001"
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 10,
      "total_count": 5,
      "total_pages": 1,
      "has_next": false,
      "has_prev": false
    }
  }
}
```

### 错误响应
```json
{
  "state": "error",
  "msgs": "缺少必传参数: spare_part_id"
}
```

或

```json
{
  "state": "error",
  "msgs": "备件不存在"
}
```

## 响应字段详细说明

### 备件信息 (spare_part_info)
| 字段名 | 类型 | 描述 |
|-------|------|------|
| id | Integer | 备件ID |
| name | String | 备件名称 |
| code | String | 备件编号 |
| model | String | 型号 |
| manufacturer | String | 厂家 |

### 使用记录 (usage_records)
| 字段名 | 类型 | 描述 |
|-------|------|------|
| id | Integer | 使用记录ID |
| equipment_id | Integer | 针对设备ID |
| equipment_name | String | 针对设备名称 |
| equipment_code | String | 针对设备编号 |
| removed_part_id | Integer | 拆下备件ID（可为null） |
| removed_part_name | String | 拆下备件名称 |
| removed_part_code | String | 拆下备件编号 |
| replacement_time | String | 更换时间（YYYY-MM-DD HH:MM:SS，已转换时区+8小时） |
| expiry_time | String | 备件寿命到期时间（YYYY-MM-DD HH:MM:SS，已转换时区+8小时） |
| remaining_days | Integer | 剩余天数 |
| usage_days | Integer | 已使用天数 |
| maintenance_order_id | Integer | 关联维修单ID（可为null） |
| maintenance_order_code | String | 关联维修单编号 |
| spare_part_name | String | 备件名称 |
| spare_part_code | String | 备件编号 |

### 分页信息 (pagination)
| 字段名 | 类型 | 描述 |
|-------|------|------|
| page | Integer | 当前页码 |
| page_size | Integer | 每页数量 |
| total_count | Integer | 总记录数 |
| total_pages | Integer | 总页数 |
| has_next | Boolean | 是否有下一页 |
| has_prev | Boolean | 是否有上一页 |

## 实现细节

### 查询逻辑
1. **参数验证**: 检查spare_part_id是否提供且为有效整数
2. **备件验证**: 验证备件是否存在
3. **条件构建**: 根据spare_part_id构建查询条件
4. **分页计算**: 计算offset和limit
5. **数据查询**: 按更换时间倒序查询使用记录
6. **数据组装**: 组装返回数据，包括时区转换

### 时间处理
```python
# 时间格式化（加8小时时区转换）
if record.replacement_time:
    replacement_time = (record.replacement_time + datetime.timedelta(hours=8)).strftime('%Y-%m-%d %H:%M:%S')

if record.expiry_time:
    expiry_time = (record.expiry_time + datetime.timedelta(hours=8)).strftime('%Y-%m-%d %H:%M:%S')
```

### 数据关联
- 自动获取针对设备信息（设备ID、名称、编号）
- 自动获取拆下备件信息（备件ID、名称、编号）
- 自动获取关联维修单信息（维修单ID、编号）
- 计算剩余天数和已使用天数

## 使用示例

### JavaScript调用示例
```javascript
async function getSparePartUsageRecords(sparePartId, page = 1, pageSize = 10) {
  try {
    const response = await fetch('/roke/spare_part/usage_records', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + token
      },
      body: JSON.stringify({
        spare_part_id: sparePartId,
        page: page,
        page_size: pageSize
      })
    });
    
    const data = await response.json();
    
    if (data.state === 'success') {
      console.log('备件信息:', data.data.spare_part_info);
      console.log('使用记录:', data.data.usage_records);
      console.log('分页信息:', data.data.pagination);
      return data.data;
    } else {
      console.error('获取失败:', data.msgs);
      return null;
    }
  } catch (error) {
    console.error('请求错误:', error);
    return null;
  }
}

// 使用示例
// 获取备件ID为1的使用记录
const records = await getSparePartUsageRecords(1);

// 获取第二页数据
const secondPage = await getSparePartUsageRecords(1, 2, 20);
```

### Vue.js组件示例
```vue
<template>
  <div class="usage-records">
    <!-- 备件信息 -->
    <div class="spare-part-info" v-if="sparePartInfo">
      <h3>{{ sparePartInfo.name }} ({{ sparePartInfo.code }})</h3>
      <p>型号: {{ sparePartInfo.model }}</p>
      <p>厂家: {{ sparePartInfo.manufacturer }}</p>
    </div>
    
    <!-- 使用记录列表 -->
    <div class="records-list">
      <div 
        v-for="record in usageRecords" 
        :key="record.id"
        class="record-card"
      >
        <div class="record-header">
          <h4>{{ record.equipment_name }} ({{ record.equipment_code }})</h4>
          <span class="replacement-time">{{ record.replacement_time }}</span>
        </div>
        
        <div class="record-details">
          <div class="detail-item">
            <label>拆下备件:</label>
            <span>{{ record.removed_part_name || '无' }}</span>
          </div>
          
          <div class="detail-item">
            <label>到期时间:</label>
            <span>{{ record.expiry_time }}</span>
          </div>
          
          <div class="detail-item">
            <label>剩余天数:</label>
            <span :class="{'expired': record.remaining_days <= 0, 'warning': record.remaining_days <= 30}">
              {{ record.remaining_days }}天
            </span>
          </div>
          
          <div class="detail-item">
            <label>已使用:</label>
            <span>{{ record.usage_days }}天</span>
          </div>
          
          <div class="detail-item" v-if="record.maintenance_order_code">
            <label>维修单:</label>
            <span>{{ record.maintenance_order_code }}</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 分页组件 -->
    <div class="pagination" v-if="pagination">
      <button 
        @click="prevPage" 
        :disabled="!pagination.has_prev"
      >
        上一页
      </button>
      <span>{{ pagination.page }} / {{ pagination.total_pages }}</span>
      <button 
        @click="nextPage" 
        :disabled="!pagination.has_next"
      >
        下一页
      </button>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    sparePartId: {
      type: Number,
      required: true
    }
  },
  
  data() {
    return {
      sparePartInfo: null,
      usageRecords: [],
      pagination: null,
      currentPage: 1,
      pageSize: 10
    };
  },
  
  mounted() {
    this.loadUsageRecords();
  },
  
  methods: {
    async loadUsageRecords() {
      const data = await getSparePartUsageRecords(
        this.sparePartId, 
        this.currentPage, 
        this.pageSize
      );
      
      if (data) {
        this.sparePartInfo = data.spare_part_info;
        this.usageRecords = data.usage_records;
        this.pagination = data.pagination;
      }
    },
    
    prevPage() {
      if (this.pagination.has_prev) {
        this.currentPage--;
        this.loadUsageRecords();
      }
    },
    
    nextPage() {
      if (this.pagination.has_next) {
        this.currentPage++;
        this.loadUsageRecords();
      }
    }
  }
};
</script>

<style scoped>
.record-card {
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.detail-item {
  display: flex;
  margin-bottom: 8px;
}

.detail-item label {
  width: 100px;
  font-weight: bold;
}

.expired {
  color: red;
}

.warning {
  color: orange;
}
</style>
```

### cURL调用示例
```bash
# 查询备件ID为1的使用记录
curl -X POST "http://localhost:8069/roke/spare_part/usage_records" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{"spare_part_id": 1, "page": 1, "page_size": 10}'

# 查询第二页数据
curl -X POST "http://localhost:8069/roke/spare_part/usage_records" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{"spare_part_id": 1, "page": 2, "page_size": 20}'
```

## 应用场景

1. **备件生命周期管理**: 跟踪备件从安装到到期的完整生命周期
2. **设备维护历史**: 查看特定备件在哪些设备上使用过
3. **备件更换分析**: 分析备件的更换频率和使用模式
4. **预防性维护**: 根据到期时间制定预防性维护计划
5. **库存管理**: 基于使用记录优化备件库存策略

## 性能考虑

1. **分页查询**: 使用limit和offset进行分页，避免一次加载过多数据
2. **索引优化**: 建议在spare_part_id和replacement_time字段上建立索引
3. **时区处理**: 统一的时区转换处理，确保时间显示正确
4. **关联查询**: 优化关联表的查询性能

## 扩展建议

1. **状态筛选**: 添加按使用状态（使用中/已到期/已更换）筛选
2. **时间范围**: 添加按更换时间范围筛选
3. **设备筛选**: 添加按设备筛选功能
4. **导出功能**: 支持导出使用记录到Excel
5. **统计信息**: 添加使用统计和分析功能

## 错误处理

### 常见错误
1. **缺少参数**: 未提供spare_part_id参数
2. **备件不存在**: 提供的spare_part_id对应的备件不存在
3. **参数格式错误**: spare_part_id不是有效的整数
4. **权限错误**: 用户没有访问备件使用记录的权限

### 错误响应格式
```json
{
  "state": "error",
  "msgs": "具体错误描述"
}
```

## 快速参考

### 请求格式
```bash
POST /roke/spare_part/usage_records
Content-Type: application/json
Authorization: Bearer <token>

{
  "spare_part_id": 1,    // 必填：备件ID
  "page": 1,             // 可选：页码，默认1
  "page_size": 10        // 可选：每页数量，默认10
}
```

### 响应格式
```json
{
  "state": "success",
  "msgs": "获取成功",
  "data": {
    "spare_part_info": {
      "id": 1,
      "name": "备件名称",
      "code": "备件编号",
      "model": "型号",
      "manufacturer": "厂家"
    },
    "usage_records": [
      {
        "id": 1,
        "equipment_name": "针对设备",
        "removed_part_name": "拆下备件",
        "replacement_time": "更换时间",
        "expiry_time": "到期时间",
        "remaining_days": 365,
        "usage_days": 30
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 10,
      "total_count": 5,
      "total_pages": 1,
      "has_next": false,
      "has_prev": false
    }
  }
}
```

### 关键特性
- ✅ 根据备件ID查询使用记录
- ✅ 返回针对设备、拆下备件、更换时间、到期时间
- ✅ 完整的分页功能
- ✅ 自动时区转换（+8小时）
- ✅ 计算剩余天数和已使用天数
- ✅ 关联维修单信息
- ✅ 支持CORS跨域访问
- ✅ 需要用户认证

## 导入到Apifox

我已经创建了一个名为 `roke_spare_part_usage_records_api_doc.json` 的文件，其中包含了完整的OpenAPI规范文档。您可以将此文件导入到Apifox中：

1. 打开Apifox
2. 点击"导入"按钮
3. 选择"OpenAPI"格式
4. 上传或粘贴`roke_spare_part_usage_records_api_doc.json`文件内容
5. 完成导入

这样您就可以在Apifox中查看和测试这个API了。
