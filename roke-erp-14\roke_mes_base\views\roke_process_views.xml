<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--工序管理-->
    <!--search-->
    <record id="view_roke_process_search" model="ir.ui.view">
        <field name="name">roke.process.search</field>
        <field name="model">roke.process</field>
        <field name="arch" type="xml">
            <search string="工序管理">
                <field name="name"/>
                <field name="code"/>
                <field name="internal_code"/>
                <field name="category_id"/>
                <filter string="已归档" name="inactive" domain="[('active', '=', False)]"/>
                <separator/>
                <filter string="主工序" name="process_main" domain="[('process_type', '=', 'main')]"/>
                <filter string="子工序" name="process_child" domain="[('process_type', '=', 'child')]"/>
                <searchpanel>
                    <field name="category_id" icon="fa-code-fork" enable_counters="1" expand="1"/>
                </searchpanel>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_process_tree" model="ir.ui.view">
        <field name="name">roke.process.tree</field>
        <field name="model">roke.process</field>
        <field name="arch" type="xml">
            <tree string="工序管理">
                <field name="internal_code" optional="hide"/>
                <field name="code"/>
                <field name="name"/>
                <field name="category_id" optional="hide"/>
                <field name="rated_working_hours" optional="hide" sum="额定工时合计"/>
                <field name="prepare_work_hours" optional="hide" sum="准备工时合计"/>
                <field name="process_type" optional="show"/>
                <field name="note" optional="show"/>
                <field name="create_uid" string="创建人" optional="show"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_process_form" model="ir.ui.view">
        <field name="name">roke.process.form</field>
        <field name="model">roke.process</field>
        <field name="arch" type="xml">
            <form string="工序管理">
                <widget name="web_ribbon" text="归档" bg_color="bg-danger" attrs="{'invisible': [('active', '=', True)]}"/>
                <group id="g1" col="4">
                    <group>
                        <field name="name"/>
                        <field name="active"/>
                        <field name="is_press" help="工装类型必须为模具"/>
                        <field name="internal_code" invisible="1"/>
                    </group>
                    <group>
                        <field name="category_id"/>
                        <!--是否委外-->
                        <field name="default_employee_ids" widget="many2many_tags" options="{'no_create_edit': True}"/>
                        <field name="process_type" required="1"/>
                    </group>
                    <group>
                        <!--采集方案-->
                        <field name="without_wo_produce"/>
                        <field name="prepare_work_hours"/>
                    </group>
                    <group>
                        <field name="rated_working_hours" attrs="{'readonly': [('child_process_ids', '!=', [])]}"/>
                        <!--采集项-->
                        <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}"/>
                    </group>
                </group>
                <notebook>
                    <page string="作业规范" name="standard_item">
                        <field name="standard_item_ids" context="{'tree_view_ref': 'roke_mes_base.view_roke_work_standard_item_editable_tree'}"/>
                    </page>
                    <page string="作业指导图片">
                        <field name="instruction_file_data" widget='image' options='{"zoom": true, "preview_image":"image_128"}'/>
                    </page>
                    <page string="子工序" attrs="{'invisible': [('process_type', '!=', 'main')]}">
                        <button type="object" name="multi_add_child_process" class="oe_highlight" string="批量添加子工序"/>
                        <field name="child_process_ids"/>
                    </page>
                </notebook>
                <group id="g2">
                    <field name="note" placeholder="此处可以填写备注或描述" />
                </group>
                <div class="oe_chatter">
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>
    <!--action-->
    <record id="view_roke_process_action" model="ir.actions.act_window">
        <field name="name">工序管理</field>
        <field name="res_model">roke.process</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{}</field>
        <field name="form_view_id" ref="view_roke_process_form"/>
        <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
            点击左上角“创建”按钮新增一个工序。
          </p><p>
            或者您也可以选择批量导入功能一次性导入多个工序。
          </p>
        </field>
    </record>

    <!--子工序-->
    <!--tree-->
    <record id="view_roke_process_child_edit_tree1" model="ir.ui.view">
        <field name="name">roke.process.child.edit.tree</field>
        <field name="model">roke.process.child</field>
        <field name="arch" type="xml">
            <tree string="子工序" editable="bottom">
                <field name="sequence"/>
                <field name="routing_line_id" invisible="1"/>
                <field name="main_process_id" invisible="1"/>
                <field name="child_process_id" domain="[('process_type','=','child')]" context="{'default_process_type': 'child'}"/>
                <field name="child_rated_wh" string="额定工时"/>
                <field name="internal_code" optional="hide" readonly="1"/>
                <field name="code" readonly="1"/>
                <field name="category_id" optional="hide" readonly="1"/>
                <field name="rated_working_hours" optional="hide" sum="额定工时合计" readonly="1"/>
                <field name="note" optional="show" readonly="1"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_process_child_form" model="ir.ui.view">
        <field name="name">roke.process.child.form</field>
        <field name="model">roke.process.child</field>
        <field name="arch" type="xml">
            <form string="子工序">
                <widget name="web_ribbon" text="归档" bg_color="bg-danger" attrs="{'invisible': [('active', '=', True)]}"/>
                <group id="g1" col="4">
                    <group>
                        <field name="name"/>
                        <field name="active"/>
                        <field name="internal_code" invisible="1"/>
                    </group>
                    <group>
                        <field name="main_process_id"/>
                        <field name="category_id"/>
                        <!--是否委外-->
                        <field name="default_employee_ids" widget="many2many_tags" options="{'no_create_edit': True}"/>
                    </group>
                    <group>
                        <!--采集方案-->
                        <field name="without_wo_produce"/>
                    </group>
                    <group>
                        <field name="rated_working_hours"/>
                        <!--采集项-->
                        <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}"/>
                    </group>
                </group>
                <notebook>
                    <page string="作业规范" name="standard_item">
                        <field name="standard_item_ids" context="{'tree_view_ref': 'roke_mes_base.view_roke_work_standard_item_editable_tree'}"/>
                    </page>
                    <page string="作业指导图片">
                        <field name="instruction_file_data" widget='image' options='{"zoom": true, "preview_image":"image_128"}'/>
                    </page>
                </notebook>
                <group id="g2">
                    <field name="note" placeholder="此处可以填写备注或描述" />
                </group>
            </form>
        </field>
    </record>

</odoo>
