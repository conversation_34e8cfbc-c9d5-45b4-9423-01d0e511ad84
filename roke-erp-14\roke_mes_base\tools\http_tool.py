# -*- coding: utf-8 -*-
"""
Description:
    接口类通用函数
Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
from odoo import models, http, fields, api, SUPERUSER_ID, _
from odoo.exceptions import ValidationError
import math
import json
import logging
import base64
import datetime
from urllib import parse

def get_selection_field_values(record, field_name):
        val = ""
        for item in record._fields[field_name].selection:
            if item[0] == record[field_name]:
                val = item[1]
                break
        return val

def selection_to_dict(model_name, field_name):
    """
    Selection 转 Dict
    获取selection字段的显示名
    如产品任务表状态字段
    模型字段定义：state = fields.Selection([("wait", "等待"), ("finish", "完成"), ···]，····)
    调用方式：http_tool.selection_to_dict("roke.mes.production.task"， "state")["wait"] # 等待
    :param model_name: 模型名
    :param field_name: selection字段名
    :return: dict->{"wait": "等待", "finish": "完成"}
    """
    return dict(
        http.request.env(user=SUPERUSER_ID)[model_name].fields_get(allfields=[field_name])[field_name]['selection']
    )


def check_id_valid(id):
    """
    校验入参id类型
    接口需入参ID时，通过该函数校验ID是否入参、是否为int类型、是否是字符串类型的数字
    :param id:
    :return: Boolean
    """
    if not id or type(id) not in (int, str):
        return False
    if type(id) in (str, ) and not id.isdigit():
        return False
    return True


def get_image_data(image_ids, file_type="url"):
    """
    获取照片预览地址
    :param image_ids:
    :return: [{}]
    """
    attachments = http.request.env(user=SUPERUSER_ID)['ir.attachment'].browse(image_ids)
    base_url = http.request.env(user=SUPERUSER_ID)['ir.config_parameter'].get_param('web.base.url')
    if file_type == "base64":
        datas = []
        for attachment in attachments:
            datas.append({
                "name": attachment.name,
                "data": attachment.datas
            })
        return datas
    else:
        preview_urls = []
        for attachment in attachments:
            if not attachment.access_token:
                attachment.generate_access_token()
            if attachment.mimetype == "application/pdf":
                # pdf 预览
                content_url = parse.quote("/web/content/%s?access_token=%s" % (
                    str(attachment.id), attachment.sudo().access_token
                ))
                url = "%s/web/static/lib/pdfjs/web/viewer.html?file=%s" % (base_url, content_url)
            else:
                url = "%s/web/image/%s?access_token=%s" % (
                    base_url, str(attachment.id), attachment.sudo().access_token
                )
            preview_urls.append({"name": attachment.name, "url": url})
        return preview_urls


def create_image_attachment(image_index, datas):
    """
    创建图片附件附件
    :param image_index: 图片标识
    :param datas: 图片base64
    :return: [ids]
    """
    attachment_ids = []
    Attachment = http.request.env['ir.attachment']
    i = 1
    for data in datas:
        attachment_ids.append(Attachment.create({
            'name': "%s-%s" % (image_index, str(i)),
            'datas': data.encode("utf-8"),
            'type': 'binary',
        }).id)
        i += 1
    return attachment_ids


def get_user_phone(user_id):
    """
    通过用户id获取该用户对应的员工电话
    :param user_id:
    :return: str
    """
    return http.request.env['hr.employee'].search([("user_id", "=", user_id)], limit=1).work_phone or ""


def get_now_work_center(user_id):
    """
    获取当前登录的工作中心
    :param user_id: 当前登录用户ID
    :return:
    """
    return http.request.env(user=SUPERUSER_ID)['roke.mes.work.center'].search([
        ("now_user_id", "=", user_id)
    ])

