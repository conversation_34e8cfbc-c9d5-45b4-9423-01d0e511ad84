<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--计量单位-->
    <!--search-->
    <record id="view_roke_uom_search" model="ir.ui.view">
        <field name="name">roke.uom.search</field>
        <field name="model">roke.uom</field>
        <field name="arch" type="xml">
            <search string="计量单位">
                <field name="name"/>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_uom_tree" model="ir.ui.view">
        <field name="name">roke.uom.tree</field>
        <field name="model">roke.uom</field>
        <field name="arch" type="xml">
            <tree string="计量单位">
                <field name="name"/>
                <field name="note" optional="show"/>
                <field name="create_uid" string="创建人"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_uom_form" model="ir.ui.view">
        <field name="name">roke.uom.form</field>
        <field name="model">roke.uom</field>
        <field name="arch" type="xml">
            <form string="计量单位">
                <sheet>
                    <group id="g1" col="4">
                        <group>
                            <field name="name"/>
                        </group>
                        <group>
                            <field name="note" widget="char"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>
    <!--action-->
    <record id="view_roke_uom_action" model="ir.actions.act_window">
        <field name="name">计量单位</field>
        <field name="res_model">roke.uom</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{}</field>
        <field name="form_view_id" ref="view_roke_uom_form"/>
        <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
            点击左上角“创建”按钮新增一个计量单位。
          </p>
        </field>
    </record>

</odoo>
