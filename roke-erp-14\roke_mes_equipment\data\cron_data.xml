<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
<!--        <record id="ir_cron_execute_scheme" model="ir.cron">-->
<!--            <field name="name">生成设备保养任务</field>-->
<!--            <field name="model_id" ref="model_roke_mes_maintenance_scheme"/>-->
<!--            <field name="state">code</field>-->
<!--            <field name="code">model.check_scheme_date()</field>-->
<!--            <field name="interval_number">1</field>-->
<!--            <field name="interval_type">days</field>-->
<!--            <field name="numbercall">-1</field>-->
<!--            <field name="doall" eval="True"/>-->
<!--            <field name="active" eval="True" />-->
<!--        </record>-->
        <!--        <record id="ir_cron_execute_eqpt_check_scheme" model="ir.cron">-->
        <!--            <field name="name">生成设备点检任务</field>-->
        <!--            <field name="model_id" ref="model_roke_mes_eqpt_spot_check_plan"/>-->
        <!--            <field name="state">code</field>-->
        <!--            <field name="code">model.check_scheme_date()</field>-->
        <!--            <field name="interval_number">1</field>-->
        <!--            <field name="interval_type">days</field>-->
        <!--            <field name="numbercall">-1</field>-->
        <!--            <field name="doall" eval="True"/>-->
        <!--            <field name="active" eval="True" />-->
        <!--        </record>-->
    </data>
</odoo>
