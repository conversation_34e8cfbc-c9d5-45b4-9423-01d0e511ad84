# -*- coding: utf-8 -*-
import os
from collections import Counter
from datetime import datetime, timedelta
import json
import logging
from itertools import groupby
from operator import attrgetter

import requests

from jinja2 import Environment, FileSystemLoader
from odoo import models, fields, api, http, SUPERUSER_ID, _

BASE_DIR = os.path.dirname(os.path.dirname(__file__))
templateloader = FileSystemLoader(searchpath=BASE_DIR + "/static")
env = Environment(loader=templateloader)

_logger = logging.getLogger(__name__)


class EnergyTj(http.Controller):

    @http.route('/roke/workstation/energy_staticfy', type='http', auth='public', csrf=False, cors="*")
    def roke_work_order_staticfy_module(self, **kwargs):
        template = env.get_template('html/abnormal_alarm/view/energy_staticfy.html')
        html = template.render({})
        return html

    @http.route('/roke/workstation/energy/energy_consumption', type='json', methods=['POST', 'OPTIONS'], auth="public",
                csrf=False, cors='*')
    def get_energy_consumption(self):

        try:
            request_data = http.request.jsonrequest
            equipment_id = request_data.get('equipment_id')
            item_code = 'ELEC001'  # 默认查询电能
            plant_id = request_data.get('plant_id')

            # 获取当前日期相关时间范围
            today = fields.Date.today()
            yesterday = today - timedelta(days=1)

            # 查询各个时间段的能耗数据
            today_data = self._get_energy_data(equipment_id, plant_id, today, item_code)
            yesterday_data = self._get_energy_data(equipment_id, plant_id, yesterday, item_code)
            monthly_data = self._get_monthly_energy_data(equipment_id, plant_id, today.year, today.month, item_code)
            yearly_data = self._get_yearly_energy_data(equipment_id, plant_id, today.year, item_code)

            return {
                "code": 200,
                'state': 'success',
                "data": {
                    "today": today_data,
                    "yesterday": yesterday_data,
                    "monthly": monthly_data,
                    "yearly": yearly_data,
                    "uom": '度'
                }
            }

        except Exception as e:
            _logger.error("获取能耗数据错误: %s", str(e))
            return {
                "code": 500,
                'state': 'error',
                "message": "获取数据失败",
                "data": {}
            }

    def _get_energy_data(self, equipment_id, plant_id, date, item_code):
        """获取指定日期的能耗数据"""
        start_date = fields.Datetime.to_string(datetime.combine(date, datetime.min.time()) - timedelta(hours=8))
        end_date = fields.Datetime.to_string(datetime.combine(date, datetime.max.time()) - timedelta(hours=8))

        return self._query_energy_data(
            equipment_id,
            plant_id,
            start_date,
            end_date,
            item_code
        )

    def _get_monthly_energy_data(self, equipment_id, plant_id, year, month, item_code):
        """获取指定月份的能耗数据"""
        start_date = datetime(year, month, 1)
        if month == 12:
            end_date = datetime.combine(datetime(year + 1, 1, 1) - timedelta(days=1), datetime.max.time())
        else:
            end_date = datetime.combine(datetime(year, month + 1, 1) - timedelta(days=1), datetime.max.time())
        start_date = fields.Datetime.to_string(start_date - timedelta(hours=8))
        end_date = fields.Datetime.to_string(end_date - timedelta(hours=8))
        return self._query_energy_data(
            equipment_id,
            plant_id,
            start_date,
            end_date,
            item_code
        )

    def _get_yearly_energy_data(self, equipment_id, plant_id, year, item_code):
        """获取指定年份的能耗数据"""
        start_date = datetime.combine(datetime(year, 1, 1), datetime.min.time())
        end_date = datetime.combine(datetime(year, 12, 31), datetime.max.time())
        start_date = fields.Datetime.to_string(start_date - timedelta(hours=8))
        end_date = fields.Datetime.to_string(end_date - timedelta(hours=8))

        return self._query_energy_data(
            equipment_id,
            plant_id,
            start_date,
            end_date,
            item_code
        )

    def _query_energy_data(self, equipment_id, plant_id, start_date, end_date, item_code):
        """通用能耗数据查询方法"""
        domain = [('consume_items_id.code', '=', item_code), ('consume_time', '>=', start_date),
                  ('consume_time', '<=', end_date)]
        if equipment_id:
            domain.append(('equipment_id', '=', equipment_id))
        if plant_id:
            domain.append(('consume_order_id.equipment_id.plant_id', '=', plant_id))
        lines = http.request.env(user=SUPERUSER_ID)['roke.ems.consume.order.line'].search(domain, order="qty desc")

        if not lines:
            return 0

        total = sum(line.qty for line in lines)

        return round(total, 2)

    @http.route('/roke/workstation/energy/today_energy_data', type='json', methods=['POST', 'OPTIONS'], auth="public",
                csrf=False, cors='*')
    def get_hourly_energy_data(self):
        today = http.request.jsonrequest.get('today')
        equipment_id = http.request.jsonrequest.get('equipment_id')
        plant_id = http.request.jsonrequest.get('plant_id')
        # 获取当前日期
        if not today:
            today = fields.Date.today()
        else:
            today = fields.Date.from_string(today)

        item = 'ELEC001'
        # 查询今日的能耗数据
        # 获取当前日期的开始和结束时间 数据库 比界面少8小时
        start_of_day = fields.Datetime.to_string(datetime.combine(today, datetime.min.time()) - timedelta(hours=8))
        end_of_day = fields.Datetime.to_string(datetime.combine(today, datetime.max.time()) - timedelta(hours=8))
        # 查询今日的能耗数据
        domain = [
            ('consume_items_id.code', '=', item),
            ('consume_time', '>=', start_of_day),
            ('consume_time', '<=', end_of_day)
        ]
        if equipment_id:
            domain.append(('equipment_id', '=', equipment_id))
        if plant_id:
            domain.append(('consume_order_id.equipment_id.plant_id', '=', plant_id))
        energy_lines = http.request.env['roke.ems.consume.order.line'].sudo().search(domain)

        # 初始化每小时能耗数据字典
        hourly_energy_data = {
        }
        for i in range(0, 24):
            hourly_energy_data[i] = 0

        if not energy_lines:
            return []

        # first_line = energy_lines[0]
        # uom_id = first_line.uom_id.id
        uom_name = '度'

        # 遍历能耗数据，累加每小时的用电量
        for line in energy_lines:
            consume_time = fields.Datetime.from_string(line.consume_time + timedelta(hours=8))
            hour = consume_time.hour
            hourly_energy_data[hour] += line.qty
        result = []
        for hour, qty in hourly_energy_data.items():
            result.append({
                "time": f'{str(hour + 1)}点',
                "consumption": round(qty, 2),
                "uom": uom_name
            })

        return {
            "code": 200,
            'state': 'success',
            'msg': '',
            "data": result
        }

    @http.route('/roke/workstation/energy/weekly_energy_data', type='json', methods=['POST', 'OPTIONS'], auth="public",
                csrf=False, cors='*')
    def get_weekly_energy_data(self):

        equipment_id = http.request.jsonrequest.get('equipment_id')
        plant_id = http.request.jsonrequest.get('plant_id')
        top = http.request.jsonrequest.get('top', 4)
        today = http.request.jsonrequest.get('today')
        # 获取当前日期
        # 计算本周时间范围
        if not today:
            today = fields.Date.today()
        else:
            today = fields.Date.from_string(today)

        start_of_week = today - timedelta(days=today.weekday())  # 本周一
        end_of_week = today + timedelta(days=6 - today.weekday())  # 本周日

        item = 'ELEC001'
        # 查询今日的能耗数据
        # 获取当前日期的开始和结束时间 数据库 比界面少8小时
        start_of_day = fields.Datetime.to_string(
            datetime.combine(start_of_week, datetime.min.time()) - timedelta(hours=8))
        end_of_day = fields.Datetime.to_string(datetime.combine(end_of_week, datetime.max.time()) - timedelta(hours=8))
        # 查询今日的能耗数据
        domain = [
            ('consume_items_id.code', '=', item),
            ('consume_time', '>=', start_of_day),
            ('consume_time', '<=', end_of_day)
        ]
        if equipment_id:
            domain.append(('equipment_id', '=', equipment_id))
        if plant_id:
            domain.append(('consume_order_id.equipment_id.plant_id', '=', plant_id))
        energy_lines = http.request.env['roke.ems.consume.order.line'].sudo().search(domain)

        # 初始化每小时能耗数据字典
        hourly_energy_data = {
        }
        for i in range(0, 24):
            hourly_energy_data[i] = 0

        if not energy_lines:
            return {
                "code": 200,
                'state': 'success',
                'msg': '',
                "data": []
            }

        # first_line = energy_lines[0]
        # uom_id = first_line.uom_id.id
        uom_name = '度'

        # 遍历能耗数据，累加每小时的用电量
        for line in energy_lines:
            consume_time = fields.Datetime.from_string(line.consume_time + timedelta(hours=8))
            hour = consume_time.hour
            hourly_energy_data[hour] += line.qty
        result = []
        for hour, qty in hourly_energy_data.items():
            if qty == 0:
                continue
            result.append({
                "time": f'{str(hour)}-{str(hour + 1)}点',
                "consumption": round(qty, 2),
                "uom": uom_name
            })
        result = sorted(result, key=lambda x: x['consumption'], reverse=True)
        if result:
            result = result[0:top]

        return {
            "code": 200,
            'state': 'success',
            'msg': '',
            "data": result
        }

    @http.route('/roke/workstation/energy/top_energy_data', type='json', methods=['POST', 'OPTIONS'], auth="public",
                csrf=False, cors='*')
    def get_top_energy_data(self):
        equipment_id = http.request.jsonrequest.get('equipment_id')
        plant_id = http.request.jsonrequest.get('plant_id')
        top = http.request.jsonrequest.get('top', 5)
        today = fields.Date.today()
        item = 'ELEC001'
        # 查询今日的能耗数据
        # 获取当前日期的开始和结束时间 数据库 比界面少8小时
        start_of_day = fields.Datetime.to_string(datetime.combine(today, datetime.min.time()) - timedelta(hours=8))
        end_of_day = fields.Datetime.to_string(datetime.combine(today, datetime.max.time()) - timedelta(hours=8))
        # 查询今日的能耗数据
        domain = [
            ('consume_items_id.code', '=', item),
            ('consume_time', '>=', start_of_day),
            ('consume_time', '<=', end_of_day)
        ]
        if equipment_id:
            domain.append(('equipment_id', '=', equipment_id))
        if plant_id:
            domain.append(('consume_order_id.equipment_id.plant_id', '=', plant_id))
        # 使用 read_group 进行分组统计
        grouped_data = http.request.env['roke.ems.consume.order.line'].sudo().read_group(
            domain=domain,
            fields=['equipment_id', 'qty:sum'],
            groupby=['equipment_id'],
            orderby='qty DESC',
            limit=top
        )

        if not grouped_data:
            return {
                "code": 200,
                "state": "success",
                "data": [],
                "uom": "度"
            }

        # 构造返回数据
        result = []
        for group in grouped_data:
            equipment_id = group['equipment_id'][0]
            equipment_name = group['equipment_id'][1]  # group['equipment_id'] => (id, name)
            total_qty = round(group['qty'], 2)
            result.append({
                "equipment_id": equipment_id,
                "equipment_name": equipment_name,
                "total_consumption": total_qty
            })

        return {
            "code": 200,
            "state": "success",
            "msg": "",
            "data": result,
            "uom": "度"
        }
