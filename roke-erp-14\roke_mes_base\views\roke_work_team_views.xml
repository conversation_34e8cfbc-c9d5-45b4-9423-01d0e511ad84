<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--班组信息-->
    <!--search-->
    <record id="view_roke_work_team_search" model="ir.ui.view">
        <field name="name">roke.work.team.search</field>
        <field name="model">roke.work.team</field>
        <field name="arch" type="xml">
            <search string="班组信息">
                <field name="name"/>
                <field name="code"/>
                <field name="manager_id"/>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_work_team_tree" model="ir.ui.view">
        <field name="name">roke.work.team.tree</field>
        <field name="model">roke.work.team</field>
        <field name="arch" type="xml">
            <tree string="班组信息">
                <field name="code"/>
                <field name="name"/>
                <field name="manager_id"/>
                <field name="note"/>
                <field name="create_uid" string="创建人"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_work_team_form" model="ir.ui.view">
        <field name="name">roke.work.team.form</field>
        <field name="model">roke.work.team</field>
        <field name="arch" type="xml">
            <form string="班组信息">
                <header>
                    <button name="action_create_qr" type="object" class="oe_highlight oe_read_only" string="生成二维码"
                            attrs="{'invisible': [('team_qr_code', '!=', False)]}"/>
                </header>
                <group id="g1">
                    <group>
                        <group>
                            <field name="code"/>
                            <field name="parent_id"/>
                        </group>
                        <group>
                            <field name="manager_id"/>
                        </group>
                    </group>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}"/>
                            <field name="restrict_work_date"/>
                            <label for="last_days" attrs="{'invisible': [('restrict_work_date', '!=', True)]}"/>
                            <div name="last_days" class="o_row"
                                 attrs="{'invisible': [('restrict_work_date', '!=', True)]}">
                                前
                                <field name="last_days" attrs="{'required': [('restrict_work_date', '=', True)]}"/>
                                天的
                                <span>
                                    <field name="last_time" widget="float_time"
                                           attrs="{'required': [('restrict_work_date', '=', True)]}"/>
                                </span>
                                后可报
                            </div>
                            <label for="after_days" attrs="{'invisible': [('restrict_work_date', '!=', True)]}"/>
                            <div name="after_days" class="o_row"
                                 attrs="{'invisible': [('restrict_work_date', '!=', True)]}">
                                后
                                <field name="after_days" attrs="{'required': [('restrict_work_date', '=', True)]}"/>
                                天的
                                <span>
                                    <field name="after_time" widget="float_time"
                                           attrs="{'required': [('restrict_work_date', '=', True)]}"/>
                                </span>
                                前可报
                            </div>
                        </group>
                        <group>
                            <field name="team_qr_code" widget="image" class="oe_avatar"/>
                        </group>
                    </group>
                </group>
                <group id="g2">
                    <field name="note" nolabel="1" placeholder="此处可以填写备注或描述" />
                </group>
                <notebook>
                    <page string="班组成员">
                        <field name="employee_line_ids">
                            <tree editable="bottom">
                                <field name="line_id" invisible="1"/>
                                <field name="employee_id" required="1" optional="show"/>
                                <field name="code" optional="show"/>
                                <field name="job_number" optional="show"/>
                                <field name="phone" optional="show"/>
                                <field name="position_id" optional="show"/>
                                <field name="department_id" optional="show"/>
                                <field name="skill_level_id" optional="show"/>
                                <field name="note" optional="hide"/>
                                <field name="team_weighted" optional="hide"/>
                                <field name="gender" optional="hide"/>
                                <field name="is_on_job" optional="hide"/>
                                <field name="id_number" optional="hide"/>
                                <field name="age" optional="hide"/>
                                <field name="registered_residence" optional="hide"/>
                            </tree>
                        </field>
                    </page>
                    <page string="产品类别">
                        <field name="product_category_ids"/>
                    </page>
                </notebook>
                <div class="oe_chatter">
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>
    <!--action-->
    <record id="view_roke_work_team_action" model="ir.actions.act_window">
        <field name="name">班组信息</field>
        <field name="res_model">roke.work.team</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{}</field>
        <field name="form_view_id" ref="view_roke_work_team_form"/>
        <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
            点击左上角“创建”按钮新增一个班组。
          </p><p>
            或者您也可以选择批量导入功能一次性导入多个班组。
          </p>
        </field>
    </record>

</odoo>
