{"openapi": "3.0.1", "info": {"title": "ROKE Spare Part API", "description": "API documentation for ROKE Spare Part module", "version": "1.0.0"}, "servers": [{"url": "http://localhost:8069", "description": "Local development server"}], "paths": {"/roke/spare_part/list": {"post": {"tags": ["Spare Part"], "summary": "获取备件数据列表", "description": "获取备件数据列表，支持按备件名称模糊搜索，支持分页", "operationId": "getSparePartList", "requestBody": {"description": "Request parameters", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetSparePartListRequest"}, "example": {"name": "轴承", "page": 1, "page_size": 10}}}, "required": false}, "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetSparePartListResponse"}, "example": {"state": "success", "msgs": "获取成功", "data": {"spare_parts": [{"id": 1, "name": "深沟球轴承", "code": "SP000001", "model": "6205-2RS", "manufacturer": "SKF", "theoretical_life": 24, "life_unit": "month", "theoretical_life_display": "24月", "usage_count": 5, "image_url": "/web/image/roke.spare.part/1/image", "uom_name": "个", "note": "高质量轴承，适用于高速运转设备"}], "pagination": {"page": 1, "page_size": 10, "total_count": 25, "total_pages": 3, "has_next": true, "has_prev": false}}}}}}}, "security": [{"odooAuth": []}]}}}, "components": {"schemas": {"GetSparePartListRequest": {"type": "object", "properties": {"name": {"type": "string", "description": "备件名称，支持模糊搜索"}, "page": {"type": "integer", "description": "页码，默认1", "default": 1, "minimum": 1}, "page_size": {"type": "integer", "description": "每页数量，默认10", "default": 10, "minimum": 1, "maximum": 100}}}, "GetSparePartListResponse": {"type": "object", "properties": {"state": {"type": "string", "description": "请求状态", "enum": ["success", "error"]}, "msgs": {"type": "string", "description": "状态消息"}, "data": {"$ref": "#/components/schemas/SparePartListData"}}}, "SparePartListData": {"type": "object", "properties": {"spare_parts": {"type": "array", "description": "备件列表", "items": {"$ref": "#/components/schemas/SparePart"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}, "SparePart": {"type": "object", "properties": {"id": {"type": "integer", "description": "备件ID"}, "name": {"type": "string", "description": "备件名称"}, "code": {"type": "string", "description": "备件编号"}, "model": {"type": "string", "description": "型号"}, "manufacturer": {"type": "string", "description": "厂家"}, "theoretical_life": {"type": "integer", "description": "理论寿命数值"}, "life_unit": {"type": "string", "description": "寿命单位", "enum": ["year", "month"]}, "theoretical_life_display": {"type": "string", "description": "理论寿命显示文本（如：24月）"}, "usage_count": {"type": "integer", "description": "已使用数量"}, "image_url": {"type": "string", "description": "图片访问URL"}, "uom_name": {"type": "string", "description": "单位名称"}, "note": {"type": "string", "description": "备注"}}}, "Pagination": {"type": "object", "properties": {"page": {"type": "integer", "description": "当前页码"}, "page_size": {"type": "integer", "description": "每页数量"}, "total_count": {"type": "integer", "description": "总记录数"}, "total_pages": {"type": "integer", "description": "总页数"}, "has_next": {"type": "boolean", "description": "是否有下一页"}, "has_prev": {"type": "boolean", "description": "是否有上一页"}}}}, "securitySchemes": {"odooAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "Odoo用户认证"}}}}