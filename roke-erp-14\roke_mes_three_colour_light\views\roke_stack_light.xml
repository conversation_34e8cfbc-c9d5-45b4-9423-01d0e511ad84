<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- 安灯 -->
    <record id="view_roke_stack_light_tree" model="ir.ui.view">
        <field name="name">roke.stack.light.tree</field>
        <field name="model">roke.stack.light</field>
        <field name="arch" type="xml">
            <tree>
                <field name="code"/>
                <field name="plant_id"/>
                <field name="state"/>
                <field name="create_date" string="创建时间"/>
                <button name="action_open_info" type="object" string="查看详情" class="oe_highlight"/>
                <button name="bind_remote_stack_light" type="object" string="绑定" class="oe_highlight"/>
            </tree>
        </field>
    </record>

    <record id="view_roke_stack_light_form" model="ir.ui.view">
        <field name="name">roke.stack.light.form</field>
        <field name="model">roke.stack.light</field>
        <field name="arch" type="xml">
            <form>
                <group>
                    <group>
                        <field name="code" required="1"/>
                        <field name="plant_id" required="1"/>
                    </group>
                </group>
            </form>
        </field>
    </record>

    <record id="action_roke_stack_light" model="ir.actions.act_window">
        <field name="name">安灯盒子</field>
        <field name="res_model">roke.stack.light</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{}</field>
    </record>

    <menuitem
            id="menu_roke_stack_light"
            name="安灯盒子"
            action="roke_mes_three_colour_light.action_roke_stack_light"
            parent="roke_three_color_light_iframe_device_monitor_menu"
            sequence="40"
            groups="base.group_system"
    />
    <!-- 安灯配置 -->
    <record id="view_roke_stack_light_config_tree" model="ir.ui.view">
        <field name="name">roke.stack.light.config.tree</field>
        <field name="model">roke.stack.light.config</field>
        <field name="arch" type="xml">
            <tree>
                <field name="color"/>
                <field name="circle_html"/>
                <field name="name"/>
                <field name="is_notify"/>
            </tree>
        </field>
    </record>

    <record id="view_roke_stack_light_config_form" model="ir.ui.view">
        <field name="name">roke.stack.light.config.form</field>
        <field name="model">roke.stack.light.config</field>
        <field name="arch" type="xml">
            <form>
                <group col="3">
                    <group>
                        <field name="color"/>
                    </group>
                    <group>
                        <field name="name"/>
                    </group>
                    <group>
                        <field name="is_notify"/>
                    </group>
                </group>
                <notebook attrs="{'invisible': [('is_notify', '!=', '是')]}">
                    <page string="通知设置">
                        <group>
                            <group>
                                <field name="notify_group_id" attrs="{'required': [('is_notify', '=', '是')]}"/>
                                <field name="notify_user_ids" widget="many2many_tags"/>
                            </group>
                            <group>
                            </group>
                        </group>
                        <group col="13">
                            <b>通知方式</b>
                            <field name="mini_program"/>
                            <field name="phone"/>
                            <field name="wechat"/>
                            <field name="dingding"/>
                            <field name="sms"/>
                            <field name="screen"/>
                        </group>
                        <div class="mb16" style="display: flex; flex-direction: row; align-items: center;">
                            <b>响应机制</b>
                            <span class="ml16">规定</span>
                            <div class="ml16" style="width: 150px">
                                <field name="response_time"/>
                            </div>
                            <span class="ml16">分钟内必须要响应，</span>
                            <div class="ml16" style="width: 150px">
                                <field name="repeat_interval"/>
                            </div>
                            <span class="ml16">分钟通知一次，未及时响应将通知</span>
                            <div class="ml16" style="width: 150px">
                                <field name="backup_group_id"/>
                            </div>
                            <span class="ml16">同时三色灯的蜂鸣声音量将调整为最大。</span>
                        </div>
                        <group>
                            <group>
                                <field name="close_user_id"/>
                            </group>
                            <group>
                                <field name="close_by_create_user"/>
                            </group>
                        </group>
                    </page>
                </notebook>
            </form>
        </field>
    </record>

    <record id="action_roke_stack_light_config" model="ir.actions.act_window">
        <field name="name">安灯配置</field>
        <field name="res_model">roke.stack.light.config</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{}</field>
    </record>

    <menuitem
            id="menu_roke_stack_light_config"
            name="安灯配置"
            action="roke_mes_three_colour_light.action_roke_stack_light_config"
            parent="roke_three_color_light_iframe_device_monitor_menu"
            sequence="30"
            groups="base.group_system"
    />
</odoo>