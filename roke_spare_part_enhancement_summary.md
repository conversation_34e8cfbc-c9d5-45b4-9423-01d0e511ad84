# roke.spare.part 模型字段增强总结

## 概述
为`roke.spare.part`模型添加了四个新字段：理论寿命、寿命单位、厂家和图片，以增强备件管理功能。

## 新增字段

### 1. 理论寿命 (theoretical_life)
- **字段类型**: Float
- **字段名称**: `theoretical_life`
- **显示名称**: "理论寿命"
- **帮助信息**: "备件的理论使用寿命，单位根据备件类型而定（如年，月，日等）"
- **用途**: 用于记录备件的预期使用寿命，便于制定维护计划和更换策略

### 2. 寿命单位 (life_unit)
- **字段类型**: Selection
- **字段名称**: `life_unit`
- **显示名称**: "寿命单位"
- **选项**: [("year", "年"), ("month", "月"), ("day", "日")]
- **默认值**: "day"
- **用途**: 配合理论寿命字段，明确寿命的时间单位

### 3. 厂家 (manufacturer)
- **字段类型**: Char
- **字段名称**: `manufacturer`
- **显示名称**: "厂家"
- **帮助信息**: "备件制造商或供应商名称"
- **用途**: 用于记录备件的制造商信息，便于采购和质量追溯

### 4. 图片 (image)
- **字段类型**: Binary
- **字段名称**: `image`
- **显示名称**: "图片"
- **帮助信息**: "备件图片，便于识别和管理"
- **用途**: 存储备件的图片，便于视觉识别和管理

## 修改的文件

### 1. 模型文件
**文件路径**: `roke-erp-14\roke_mes_equipment\models\roke_spare_part.py`

**修改内容**:
```python
class RokeSparePart(models.Model):
    _name = "roke.spare.part"
    _description = "备件"

    name = fields.Char(string="名称")
    code = fields.Char(string="编号")
    model = fields.Char(string="型号")
    uom_id = fields.Many2one("roke.uom", string="单位")
    theoretical_life = fields.Float(string="理论寿命", help="备件的理论使用寿命，单位根据备件类型而定（如年，月，日等）")
    life_unit = fields.Selection([("year", "年"), ("month", "月"), ("day", "日")], string="寿命单位", default="day")
    manufacturer = fields.Char(string="厂家", help="备件制造商或供应商名称")
    image = fields.Binary(string="图片", help="备件图片，便于识别和管理")
    note = fields.Text(string="备注")
```

### 2. 视图文件
**文件路径**: `roke-erp-14\roke_mes_equipment\views\roke_spare_part_views.xml`

**新增内容**:
- 搜索视图：支持按厂家搜索和分组
- 列表视图：显示所有字段包括新增的理论寿命和厂家
- 表单视图：将字段分组为"基本信息"和"技术参数"两个组
- 操作视图：定义备件管理的窗口操作

### 3. 模块配置文件
**文件路径**: `roke-erp-14\roke_mes_equipment\__manifest__.py`

**修改内容**:
- 在data列表中添加了`'views/roke_spare_part_views.xml'`
- 在demo列表中添加了`'data/spare_part_demo_data.xml'`

### 4. 模型初始化文件
**文件路径**: `roke-erp-14\roke_mes_equipment\models\__init__.py`

**修改内容**:
- 添加了`from . import roke_spare_part`导入语句

### 5. 安全权限文件
**文件路径**: `roke-erp-14\roke_mes_equipment\security\ir.model.access.csv`

**修改内容**:
- 添加了`access_roke_spare_part,roke_spare_part,model_roke_spare_part,base.group_system,1,1,1,1`权限配置

### 6. 菜单文件
**文件路径**: `roke-erp-14\roke_mes_equipment\views\menus.xml`

**修改内容**:
- 在设备管理菜单下添加了"备件管理"菜单项

### 7. 演示数据文件
**文件路径**: `roke-erp-14\roke_mes_equipment\data\spare_part_demo_data.xml`

**新增内容**:
- 创建了4个备件演示记录，展示不同类型备件的理论寿命和厂家信息

## 界面布局

### 表单视图布局
```xml
<form string="备件">
    <sheet>
        <div class="oe_button_box" name="button_box">
            <field name="image" widget="image" class="oe_avatar"/>
        </div>
        <group>
            <group string="基本信息">
                <field name="name" required="1"/>
                <field name="code"/>
                <field name="model"/>
                <field name="uom_id"/>
            </group>
            <group string="技术参数">
                <field name="manufacturer"/>
                <field name="theoretical_life"/>
                <field name="life_unit"/>
            </group>
        </group>
        <group string="备注">
            <field name="note" nolabel="1"/>
        </group>
    </sheet>
</form>
```

### 列表视图字段
- 图片 (image) - 40x40像素缩略图
- 编号 (code)
- 名称 (name)
- 型号 (model)
- 厂家 (manufacturer)
- 理论寿命 (theoretical_life)
- 寿命单位 (life_unit)
- 单位 (uom_id)

## 演示数据示例

1. **深沟球轴承**
   - 编号: SP001
   - 型号: 6205-2RS
   - 厂家: SKF
   - 理论寿命: 2年

2. **同步带**
   - 编号: SP002
   - 型号: HTD-8M-1600
   - 厂家: 盖茨
   - 理论寿命: 6个月

3. **液压滤芯**
   - 编号: SP003
   - 型号: HF6555
   - 厂家: 弗列加
   - 理论寿命: 90天

4. **O型密封圈**
   - 编号: SP004
   - 型号: O-50x3
   - 厂家: NOK
   - 理论寿命: 1年

## 功能特性

1. **搜索功能**: 支持按名称、编号、型号、厂家进行搜索
2. **分组功能**: 支持按厂家和单位进行分组显示
3. **图片管理**: 支持上传和显示备件图片，列表视图显示缩略图
4. **寿命管理**: 理论寿命和寿命单位分离，支持年、月、日三种单位
5. **权限控制**: 只有系统管理员组用户可以访问
6. **数据完整性**: 名称字段设为必填
7. **用户友好**: 提供详细的帮助信息和字段说明

## 使用说明

1. **访问路径**: 设备管理 → 设备菜单 → 备件管理
2. **创建备件**: 点击"创建"按钮，填写备件基本信息和技术参数
3. **上传图片**: 在表单视图右上角点击图片区域上传备件图片
4. **理论寿命**: 填写寿命数值并选择对应的时间单位（年/月/日）
5. **厂家信息**: 填写准确的制造商或供应商名称
6. **搜索过滤**: 使用搜索框快速查找特定备件
7. **分组查看**: 使用分组功能按厂家或单位组织显示
8. **图片预览**: 在列表视图中可以看到备件的缩略图

## 注意事项

1. 理论寿命现在有专门的单位字段，支持年、月、日三种时间单位
2. 厂家信息应保持一致性，避免同一厂家的不同写法
3. 图片文件建议使用常见格式（JPG、PNG等），文件大小适中
4. 演示数据仅在演示模式下加载，生产环境不会自动创建
5. 修改后需要重启Odoo服务并升级模块才能生效

## 后续扩展建议

1. 可以考虑将厂家字段改为Many2one关系，关联到供应商模型
2. 可以添加备件分类字段，便于管理不同类型的备件
3. 可以添加库存相关字段，如最小库存量、当前库存等
4. 可以添加成本相关字段，如采购价格、标准成本等
5. 可以添加多图片支持，允许上传多张备件图片
6. 可以添加二维码字段，便于移动端扫码识别
7. 可以添加备件状态字段，如在用、备用、报废等
