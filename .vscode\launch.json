{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "iot",
            "type": "python",
            "python": "D:/projects/odoo/venv/odoo14-venv/Scripts/python.exe",
            "request": "launch",
            "program": "D:/projects/odoo/iot/roke-odoo14/odoo-14.0/odoo-bin",
            "args": [
                "--config=D:/projects/odoo/iot/.vscode/odoo14-iot.conf",
                // "-d",
                // "iot",
                // "-u",
                // "roke_mes_three_colour_light"
            ],
            "console": "integratedTerminal",
            "justMyCode": true
        },
        {
            "name": "qdry-iot",
            "type": "python",
            "python": "D:/projects/odoo/venv/odoo14-venv/Scripts/python.exe",
            "request": "launch",
            "program": "D:/projects/odoo/iot/roke-odoo14/odoo-14.0/odoo-bin",
            "args": [
                "--config=D:/projects/odoo/iot/.vscode/odoo14-qdry.conf",
                "-d",
                "qdry-iot",
                "-u",
                "roke_mes_three_colour_light"
            ],
            "console": "integratedTerminal",
            "justMyCode": true
        },
        {
            "name": "test",
            "type": "python",
            "python": "D:/projects/odoo/venv/odoo14-venv/Scripts/python.exe",
            "request": "launch",
            "program": "D:/projects/odoo/iot/test.py",
            "args": [
                // "--config=D:/projects/odoo/config/odoo14-iot.conf",
            ],
            "console": "integratedTerminal",
            "justMyCode": true
        },
    ]
}