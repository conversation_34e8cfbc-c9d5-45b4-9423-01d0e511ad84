<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8" />
  <title>设备统计</title>
  <meta content="width=device-width,initial-scale=1.0, maximum-scale=1.0,user-scalable=0" name="viewport" />
  <!-- /roke_workstation_api/static/html/routing -->
  <link rel="stylesheet" href="/roke_workstation_api/static/html/routing/element-ui/index.css" />
  <script src="/roke_workstation_api/static/html/routing/js/echarts.min.js"></script>
  <script src="/roke_workstation_api/static/html/routing/js/moment.min.js"></script>
  <script src="/roke_workstation_api/static/html/routing/js/vue.js"></script>
  <script src="/roke_workstation_api/static/html/routing/js/axios.min.js"></script>
  <script src="/roke_workstation_api/static/html/routing/element-ui/index.js"></script>
</head>

<body id="bodyId" style="display: none">
  <div id="app" v-loading.body.fullscreen.lock="loading">
    <!-- 顶部统计卡片 -->
    <div class="card-row">
      <div class="stat-card">
        <div class="stat-value">[[ cardData.total || 0 ]]</div>
        <div class="stat-label">设备总数</div>
      </div>
      <div class="stat-card">
        <div class="stat-value">[[ cardData.green || 0 ]]</div>
        <div class="stat-label">运行正常</div>
      </div>
      <div class="stat-card">
        <div class="stat-value">[[ cardData.red || 0 ]]</div>
        <div class="stat-label">故障中</div>
      </div>
      <div class="stat-card">
        <div class="stat-value">[[ cardData.yellow || 0 ]]</div>
        <div class="stat-label">空闲中</div>
      </div>
    </div>
    <!-- 图表区域：两行三列 -->
    <div class="chart-row">
      <div class="chart-box">
        <div class="chart-title">设备综合效率</div>
        <div id="deviceSynthesisEffChart" class="chart-content"></div>
      </div>
      <div class="chart-box">
        <div class="chart-title">今天待点检&维修设备</div>
        <div id="holdPointServicingDeviceChart" class="chart-content"></div>
      </div>
      <div class="chart-box">
        <div class="chart-title">近一周设备点检情况</div>
        <div id="deviceInspectionSituationChart" class="chart-content"></div>
      </div>
      <div class="chart-box">
        <div class="chart-title">近一周设备保养情况</div>
        <div id="deviceMaintenanceConditionChart" class="chart-content"></div>
      </div>
      <div class="chart-box">
        <div class="chart-title">近一周设备维修情况</div>
        <div id="deviceRepairChart" class="chart-content"></div>
      </div>
      <div class="chart-box">
        <div class="chart-title">备件更换情况</div>
        <div id="sparePartReplaceStatusChart" class="chart-content"></div>
      </div>
    </div>
  </div>
</body>

<script>
  // 发送消息给父页面(关闭odoo的菜单弹窗)
  document.addEventListener("click", () => {
    window.parent.postMessage("hidePopover", "*")
  })
  let vue = new Vue({
    el: "#app",
    delimiters: ["[[", "]]"], // 替换原本vue的[[ key ]]取值方式(与odoo使用的jinja2冲突问题)
    data() {
      return {
        windowHeight: window.innerHeight, // 窗口高度
        baseURL: "", // 基地址 https://yanshi-workstation.xbg.rokeris.com
        dwsBaseUrl: "https://dws-platform.xbg.rokeris.com/dev-api", // dws系统基地址
        loading: false, // 全局加载效果
        cardData: {},
        charts: {}
      }
    },
    async created() {
      this.loading = true
      // 添加resize事件监听
      window.addEventListener("resize", this.handleResize)
      const apiList = [
        this.getEquipmentCountFn(),
        this.getStackLightStateFn(),
        this.getTodayCheckRepairFn(),
        this.getWeeklyCheckAnomaliesFn(),
        this.getWeeklyMaintenanceStatsFn(),
        this.getWeeklyRepairStatsFn(),
        this.getChangeListFn()
      ]
      // 使用Promise.all等待所有请求完成
      await Promise.all(apiList).then(responses => {
        // responses是一个数组，按请求顺序排列
        const equipmentCountResponse = responses[0]
        const stackLightStateResponse = responses[1]
        const todayCheckRepairResponse = responses[2]
        const weeklyCheckAnomaliesResponse = responses[3]
        const weeklyMaintenanceStatsResponse = responses[4]
        const weeklyRepairStatsResponse = responses[5]
        const changeListResponse = responses[6]
        // 设备统计数据
        this.cardData = equipmentCountResponse || {}
        this.$nextTick(() => {
          // 设备综合效率（柱状图）
          this.initDeviceSynthesisEffChart(stackLightStateResponse)
          // 今天待点检&维修设备（饼图）
          this.initHoldPointServicingDeviceChart(todayCheckRepairResponse)
          // 近一周设备点检情况（折线图）
          this.initDeviceInspectionSituationChart(weeklyCheckAnomaliesResponse)
          // 近一周设备保养情况（柱状图）
          this.initDeviceMaintenanceConditionChart(weeklyMaintenanceStatsResponse)
          // 近一周设备维修情况（折线图）
          this.initDeviceRepairChart(changeListResponse)
          // 备件更换情况（柱状图）
          this.initSparePartReplaceStatusChart(changeListResponse)
        })
      }).catch(error => {
        console.error('至少一个请求失败:', error)
      })
      this.loading = false
    },
    mounted() {
      this.$nextTick(() => {
        document.getElementById("bodyId").style.display = "block"
      })
    },
    methods: {
      // 处理窗口大小变化修改图表大小
      handleResize() {
        for (let key in this.charts) {
          this.charts[key] && this.charts[key].resize()
        }
      },
      // 接口请求方法封装
      requestApi(
        url,
        config = {},
        errorMessage = "操作失败，请稍后重试",
        contentType = "application/json"
      ) {
        return new Promise((resolve, reject) => {
          if (!url) reject(null)
          axios({
            method: "POST",
            url: this.baseURL + url,
            data: config,
            headers: { "Content-Type": contentType },
          }).then((result) => {
            if (
              result?.data?.result?.code == 0 ||
              result?.data?.result?.state == "success" ||
              result?.data?.code == 0
            ) {
              resolve(result.data)
            } else if (result?.data?.result?.code == 1) {
              reject(result.data.result.message)
            } else if (result?.data?.result?.state == "error") {
              reject(result.data.result.megs)
            } else if (result?.data?.code == 0) {
              reject(result.data.message)
            } else if (result?.data?.error) {
              reject(result.data.error.message)
            }
          }).catch((error) => {
            reject(errorMessage)
          })
        })
      },
      // 接口请求Dws系统方法封装
      requestDwsApi(
        url,
        config = {},
        errorMessage = "操作失败，请稍后重试",
        contentType = "application/json"
      ) {
        return new Promise((resolve, reject) => {
          if (!url) reject(null)
          axios({
            method: "POST",
            url: this.dwsBaseUrl + url,
            data: config,
            headers: { "Content-Type": contentType },
          }).then((result) => {
            if (result?.data?.success) {
              resolve(result.data)
            } else if (!result.data.success) {
              reject(result.data.msg)
            } else {
              reject(errorMessage)
            }
          }).catch((error) => {
            reject(errorMessage)
          })
        })
      },
      // 获取设备统计
      getEquipmentCountFn() {
        return new Promise(async (resolve, reject) => {
          await this.requestApi("/roke/workstation/equipment/get_equipment_count").then((res) => {
            resolve(res.result.data || {})
          }).catch((error) => {
            reject(error)
          })
        })
      },
      // 获取设备综合效率
      getStackLightStateFn() {
        return new Promise(async (resolve, reject) => {
          await this.requestApi("/roke/workstation/work_record/stack_light_state", {
            "page": 1,
            "page_size": 9999
          }).then((res) => {
            resolve(res.result.data || [])
          }).catch((error) => {
            reject(error)
          })
        })
      },
      // 获取今日待检&维修统计
      getTodayCheckRepairFn() {
        return new Promise(async (resolve, reject) => {
          await this.requestApi("/roke/workstation/equipment/today_check_repair").then((res) => {
            resolve(res.result.data || {})
          }).catch((error) => {
            reject(error)
          })
        })
      },
      // 获取近一周设备点检情况
      getWeeklyCheckAnomaliesFn() {
        return new Promise(async (resolve, reject) => {
          await this.requestApi("/roke/workstation/equipment/weekly_check_anomalies").then((res) => {
            resolve(res.result.data || [])
          }).catch((error) => {
            reject(error)
          })
        })
      },
      // 获取近一周设备保养情况
      getWeeklyMaintenanceStatsFn() {
        return new Promise(async (resolve, reject) => {
          await this.requestApi("/roke/workstation/equipment/weekly_maintenance_stats", {
            "page": 1,
            "page_size": 9999
          }).then((res) => {
            resolve(res.result.data || [])
          }).catch((error) => {
            reject(error)
          })
        })
      },
      // 获取近一周设备维修情况
      getWeeklyRepairStatsFn() {
        return new Promise(async (resolve, reject) => {
          await this.requestApi("/roke/workstation/equipment/weekly_repair_stats", {
            "page": 1,
            "page_size": 9999
          }).then((res) => {
            resolve(res.result.data || [])
          }).catch((error) => {
            reject(error)
          })
        })
      },
      // 获取近一周设备维修情况
      getChangeListFn() {
        return new Promise(async (resolve, reject) => {
          await this.requestApi("/roke/workstation/equipment/change_list", {
            "page": 1,
            "page_size": 9999
          }).then((res) => {
            resolve(res.result.data || [])
          }).catch((error) => {
            reject(error)
          })
        })
      },
      // 设备综合效率（柱状图）
      initDeviceSynthesisEffChart(data) {
        const chart = echarts.init(document.getElementById('deviceSynthesisEffChart'))
        this.charts.deviceSynthesisEff = chart
        // 单位
        const unit = ''
        // x轴数据
        const xAxisData = []
        // 数量
        const seriesData = []
        data.forEach((item) => {
          xAxisData.push(item.name || item.code || '')
          seriesData.push(item.utilization_rate || 0)
        })
        setTimeout(() => {
          chart.setOption({
            grid: { left: '5%', right: '5%', top: '20%', bottom: '2%', containLabel: true },
            tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' }, valueFormatter: (value) => value + unit },
            legend: { left: 'center', data: ['加工率'], textStyle: { color: "#fff", fontSize: 10 } },
            dataZoom: [{ id: 'deviceSynthesisEffChart', type: 'inside', end: (5 / xAxisData.length) * 100 }],
            xAxis: {
              type: 'category',
              data: xAxisData,
              axisLine: { lineStyle: { color: '#0a73ff' } },
              axisLabel: { color: '#fff', fontSize: 10 }
            },
            yAxis: {
              type: 'value',
              min: 0,
              axisLine: { show: true, lineStyle: { color: '#0a73ff' } },
              splitLine: { lineStyle: { color: '#0a73ff', width: 0.5, opacity: 0.3 } },
              axisLabel: { color: '#fff', fontSize: 10, formatter: '{value}' + unit }
            },
            series: [{
              name: '加工率',
              type: 'bar',
              barWidth: '40%',
              barGap: '0%',
              data: seriesData,
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#01c4f9' },
                  { offset: 1, color: '#0a73ff' }
                ])
              },
              label: { show: true, position: 'top', color: '#FFF', fontSize: 10, formatter: (params) => params.value + unit }
            }]
          })
        }, 1)
      },
      // 今天待点检&维修设备（饼图）
      initHoldPointServicingDeviceChart(data) {
        const chart = echarts.init(document.getElementById('holdPointServicingDeviceChart'))
        this.charts.holdPointServicingDevice = chart
        // 单位
        const unit = "台"
        setTimeout(() => {
          chart.setOption({
            tooltip: { trigger: 'item', valueFormatter: (value) => value + unit },
            legend: { left: 'center', textStyle: { color: "#fff", fontSize: 10 } },
            series: [{
              type: 'pie',
              radius: '65%',
              center: ['50%', '55%'],
              itemStyle: { borderRadius: 3, borderColor: '#131d58', borderWidth: 2 },
              label: { show: true, formatter: '{b}: {d}' + unit, color: '#fff', fontSize: 12 },
              data: [
                { value: data?.pending_check_count || 0, name: '待点检', itemStyle: { color: '#5187eb' } },
                { value: data?.pending_repair_count || 0, name: '待维修', itemStyle: { color: '#e34546' } }
              ]
            }]
          })
        }, 1)
      },
      // 近一周设备点检情况（折线图）
      initDeviceInspectionSituationChart(data) {
        const chart = echarts.init(document.getElementById('deviceInspectionSituationChart'))
        this.charts.deviceInspectionSituation = chart
        // 单位
        const unit = ''
        // x轴数据
        const xAxisData = []
        // 数量
        const seriesData = []
        data.forEach((item) => {
          xAxisData.push(item.date || '')
          seriesData.push(item.anomaly_count || 0)
        })
        setTimeout(() => {
          chart.setOption({
            grid: { left: '5%', right: '5%', top: '20%', bottom: '2%', containLabel: true },
            tooltip: { trigger: 'axis', valueFormatter: (value) => value + unit },
            legend: { left: 'center', data: ['异常次数'], textStyle: { color: "#fff", fontSize: 10 } },
            xAxis: {
              type: 'category',
              boundaryGap: false,
              data: xAxisData,
              axisLine: { show: true, lineStyle: { color: '#0a73ff' } },
              axisTick: { show: false },
              axisLabel: { color: '#fff', fontSize: 10, interval: 0 }
            },
            yAxis: {
              type: 'value',
              min: 0,
              axisLine: { show: true, lineStyle: { color: '#0a73ff' } },
              splitLine: { lineStyle: { color: '#0a73ff', width: 0.5, opacity: 0.3, type: 'dashed' } },
              axisLabel: { color: '#ccc', fontSize: 10, formatter: '{value}' + unit }
            },
            series: [{
              name: '异常次数',
              type: 'line',
              data: seriesData,
              label: {
                show: true,
                position: 'top',
                color: '#FFF',
                fontSize: 10,
                formatter: (params) => params.value + unit
              }
            }]
          })
        }, 1)
      },
      // 近一周设备保养情况（柱状图）
      initDeviceMaintenanceConditionChart(data) {
        const chart = echarts.init(document.getElementById('deviceMaintenanceConditionChart'))
        this.charts.deviceMaintenanceCondition = chart
        // 单位
        const unit = ''
        // x轴数据
        const xAxisData = []
        // 数量
        const seriesData = []
        data.forEach((item) => {
          xAxisData.push(item.equipment_name || '')
          seriesData.push(item.maintenance_count || 0)
        })
        setTimeout(() => {
          chart.setOption({
            grid: { left: '5%', right: '5%', top: '20%', bottom: '2%', containLabel: true },
            tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' }, valueFormatter: (value) => value + unit },
            legend: { left: 'center', data: ['保养次数'], textStyle: { color: "#fff", fontSize: 10 } },
            dataZoom: [{ id: 'deviceSynthesisEffChart', type: 'inside', end: (5 / xAxisData.length) * 100 }],
            xAxis: {
              type: 'category',
              data: xAxisData,
              axisLine: { lineStyle: { color: '#0a73ff' } },
              axisLabel: { color: '#fff', fontSize: 10 }
            },
            yAxis: {
              type: 'value',
              min: 0,
              axisLine: { show: true, lineStyle: { color: '#0a73ff' } },
              splitLine: { lineStyle: { color: '#0a73ff', width: 0.5, opacity: 0.3 } },
              axisLabel: { color: '#fff', fontSize: 10, formatter: '{value}' + unit }
            },
            series: [{
              name: '保养次数',
              type: 'bar',
              barWidth: '40%',
              barGap: '0%',
              data: seriesData,
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#01c4f9' },
                  { offset: 1, color: '#0a73ff' }
                ])
              },
              label: { show: true, position: 'top', color: '#FFF', fontSize: 10, formatter: (params) => params.value + unit }
            }]
          })
        }, 1)
      },
      // 近一周设备维修情况（折线图）
      initDeviceRepairChart(data) {
        const chart = echarts.init(document.getElementById('deviceRepairChart'))
        this.charts.deviceRepair = chart
        // 单位
        const unit = ''
        // x轴数据
        const xAxisData = []
        // 数量
        const seriesData = []
        data.forEach((item) => {
          xAxisData.push(item.date || '')
          seriesData.push(item.repair_count || 0)
        })
        setTimeout(() => {
          chart.setOption({
            grid: { left: '5%', right: '5%', top: '20%', bottom: '2%', containLabel: true },
            tooltip: { trigger: 'axis', valueFormatter: (value) => value + unit },
            legend: { left: 'center', data: ['维修次数'], textStyle: { color: "#fff", fontSize: 10 } },
            xAxis: {
              type: 'category',
              boundaryGap: false,
              data: xAxisData,
              axisLine: { show: true, lineStyle: { color: '#0a73ff' } },
              axisTick: { show: false },
              axisLabel: { color: '#fff', fontSize: 10, interval: 0 }
            },
            yAxis: {
              type: 'value',
              min: 0,
              axisLine: { show: true, lineStyle: { color: '#0a73ff' } },
              splitLine: { lineStyle: { color: '#0a73ff', width: 0.5, opacity: 0.3, type: 'dashed' } },
              axisLabel: { color: '#ccc', fontSize: 10, formatter: '{value}' + unit }
            },
            series: [{
              name: '维修次数',
              type: 'line',
              data: seriesData,
              label: {
                show: true,
                position: 'top',
                color: '#FFF',
                fontSize: 10,
                formatter: (params) => params.value + unit
              }
            }]
          })
        }, 1)
      },
      // 备件更换情况（柱状图）
      initSparePartReplaceStatusChart(data) {
        const chart = echarts.init(document.getElementById('sparePartReplaceStatusChart'))
        this.charts.sparePartReplaceStatus = chart
        // 单位
        const unit = ''
        // x轴数据
        const xAxisData = []
        // 数量
        const seriesData = []
        data.forEach((item) => {
          xAxisData.push(item.equipment_name || '')
          seriesData.push(item.change_count || 0)
        })
        setTimeout(() => {
          chart.setOption({
            grid: { left: '5%', right: '5%', top: '20%', bottom: '2%', containLabel: true },
            tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' }, valueFormatter: (value) => value + unit },
            legend: { left: 'center', data: ['更换次数'], textStyle: { color: "#fff", fontSize: 10 } },
            dataZoom: [{ id: 'deviceSynthesisEffChart', type: 'inside', end: (5 / xAxisData.length) * 100 }],
            xAxis: {
              type: 'category',
              data: xAxisData,
              axisLine: { lineStyle: { color: '#0a73ff' } },
              axisLabel: { color: '#fff', fontSize: 10 }
            },
            yAxis: {
              type: 'value',
              min: 0,
              axisLine: { show: true, lineStyle: { color: '#0a73ff' } },
              splitLine: { lineStyle: { color: '#0a73ff', width: 0.5, opacity: 0.3 } },
              axisLabel: { color: '#fff', fontSize: 10, formatter: '{value}' + unit }
            },
            series: [{
              name: '更换次数',
              type: 'bar',
              barWidth: '40%',
              barGap: '0%',
              data: seriesData,
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#01c4f9' },
                  { offset: 1, color: '#0a73ff' }
                ])
              },
              label: { show: true, position: 'top', color: '#FFF', fontSize: 10, formatter: (params) => params.value + unit }
            }]
          })
        }, 1)
      }
    },
    beforeDestroy() {
      // 清除resize事件监听
      window.removeEventListener("resize", this.handleResize)
    }
  })
</script>

<style>
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  ::-webkit-scrollbar {
    width: 0px;
    height: 0px;
  }

  ::-webkit-scrollbar-thumb {
    background: #c0c4cc;
    border-radius: 3px;
  }

  ::-webkit-scrollbar-track {
    background: #f5f7fa;
  }

  #app {
    width: 100vw;
    height: 100vh;
    overflow: hidden;
    color: #fff;
    font-family: "Microsoft YaHei", sans-serif;
    background-color: #06114f;
    padding: 10px;
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .card-row {
    display: flex;
    gap: 10px;

    .stat-card {
      flex: 1;
      background: rgba(255, 255, 255, 0.05);
      border-radius: 8px;
      padding: 24px;
      text-align: center;
      border: 2px solid rgba(255, 255, 255, 0.2);

      .stat-value {
        font-size: 32px;
        font-weight: bold;
      }

      .stat-label {
        font-size: 16px;
      }
    }
  }

  .chart-row {
    flex: auto;
    height: 1px;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }

  .chart-box {
    width: calc((100% - 20px) / 3);
    height: calc((100% - 10px) / 2);
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 8px;
    display: flex;
    flex-direction: column;
    gap: 8px;

    .chart-title {
      font-size: 16px;
      font-weight: bold;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .chart-content {
      width: 100%;
      height: 100%;
    }
  }
</style>

</html>