# -*- coding: utf-8 -*-

from odoo import models, fields, api, http, SUPERUSER_ID, _
from odoo.exceptions import UserError, AccessDenied
from odoo.addons.roke_mes_base.tools import http_tool
import math
import json
import logging
import datetime
import requests
from io import StringIO
import traceback
import logging
_logger = logging.getLogger(__name__)

class RokeMesAppEquipment(http.Controller):


    @http.route('/roke/equipment/create_inspection_project', type='json', methods=['POST', 'OPTIONS'], auth="user",
                csrf=False, cors='*')
    def create_inspection_project(self):
        """
        创建设备点检计划
        :param equipment_id: 设备ID
        :param check_plan_id: 点检方案ID
        :param frequency_type: 频率类型 ('daily', 'weekly', 'monthly')
        :param start_date: 生成时间（datetime格式字符串）
        :param end_date: 结束时间（datetime格式字符串）
        :param active: 状态（是否启用），布尔值，默认 True
        :param check_user_id
        :return:
        """
        data = http.request.jsonrequest

        required_fields = ['equipment_id', 'check_plan_id', 'frequency_type', 'start_date', 'end_date','active']
        for field in required_fields:
            if not data.get(field):
                return {
                    "state": "error",
                    "msgs": f"缺少必填字段：{field}",
                    "data":{}

                }

        try:
            vals = {
                'equipment_id': int(data.get('equipment_id')),
                'check_plan_id': int(data.get('check_plan_id')),
                'frequency_type': data.get('frequency_type'),
                'start_date': data.get('start_date'),  # 格式应为 YYYY-MM-DD HH:mm:ss
                'end_date': data.get('end_date'),  # 格式应为 YYYY-MM-DD HH:mm:ss
                'active': data.get('active',1),  # 默认启用
                'check_user_id': data.get('check_user_id'),
            }
            if not  vals['check_user_id']:
                vals['check_user_id'] = http.request.env.user.id or SUPERUSER_ID

            vals['start_date'] = fields.Datetime.from_string(vals['start_date']) - datetime.timedelta(hours=8)
            vals['end_date'] = fields.Datetime.from_string(vals['end_date']) - datetime.timedelta(hours=8)

            vals['start_date'] = fields.Datetime.to_string(vals['start_date'])
            vals['end_date'] = fields.Datetime.to_string(vals['end_date'])


            # 创建点检计划
            project = http.request.env['roke.mes.equipment.inspection.project'].sudo().create(vals)
            return {
                "state": "success",
                "msgs": "点检计划创建成功",
                "data":{
                    "project_id": project.id,
                    "project_code": project.code,
                    'note': project.note,
                }

            }

        except Exception as e:
            _logger.error(e)
            if project:
                project.unlink()
            return {
                "state": "error",
                "msgs": str(e),
                "data":{}
            }

    @http.route('/roke/equipment/get_inspection_projects', type='json', methods=['POST', 'OPTIONS'], auth="user",
                csrf=False, cors='*')
    def get_inspection_projects(self):
        """
        获取点检计划数据（扁平化输出）
        :param equipment_id: （可选）设备ID
        :return:
            {
            }
        """
        data = http.request.jsonrequest
        equipment_id = data.get('equipment_id')  # 可选参数

        domain = []
        if equipment_id:
            domain.append(('equipment_id', '=', int(equipment_id)))

        projects = http.request.env['roke.mes.equipment.inspection.project'].sudo().search(domain)

        result = []
        for project in projects:
            result.append({
                "project_id": project.id,
                "code": project.code or "",

                # 设备字段
                "equipment_id": project.equipment_id.id,
                "equipment_code": project.equipment_id.code or "",
                "equipment_name": project.equipment_id.name or "",

                # 点检方案字段
                "check_plan_id": project.check_plan_id.id,
                "check_plan_name": project.check_plan_id.name or "",
                "check_plan_type": project.check_plan_id.type or "",

                # 计划字段
                "frequency_type": project.frequency_type or "",
                "frequency_type_name": project.get_selection_field_values("frequency_type",  project.frequency_type),
                "start_date": (project.start_date + datetime.timedelta(hours=8)).strftime('%Y-%m-%d %H:%M:%S') if project.start_date else "",
                "end_date": (project.end_date + datetime.timedelta(hours=8)).strftime('%Y-%m-%d %H:%M:%S') if project.end_date else "",
                "note": project.note or "",
                "active": project.active,
            })

        return {
            "state": "success",
            "msgs": "获取成功",
            "data": result
        }



