<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--业务伙伴-->
    <!--search-->
    <record id="view_roke_partner_search" model="ir.ui.view">
        <field name="name">roke.partner.search</field>
        <field name="model">roke.partner</field>
        <field name="arch" type="xml">
            <search string="业务伙伴">
                <field name="name"/>
                <field name="code"/>
                <field name="contacts"/>
                <field name="contacts_phone"/>
                <field name="address"/>
                <field name="tag_ids" string="标签"/>
<!--                 filter_domain="[('tag_ids', 'in', self)]"-->
                <filter string="已归档" name="inactive" domain="[('active', '=', False)]"/>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_partner_tree" model="ir.ui.view">
        <field name="name">roke.partner.tree</field>
        <field name="model">roke.partner</field>
        <field name="arch" type="xml">
            <tree string="业务伙伴">
                <field name="code"/>
                <field name="name"/>
                <field name="contacts"/>
                <field name="contacts_phone"/>
                <field name="address"/>
                <field name="legal_person" optional="hide"/>
                <field name="tag_ids" optional="show" widget="many2many_tags" options="{'color_field': 'color'}"/>
                <field name="create_uid" string="创建人"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_partner_form" model="ir.ui.view">
        <field name="name">roke.partner.form</field>
        <field name="model">roke.partner</field>
        <field name="arch" type="xml">
            <form string="业务伙伴">
                <header/>
                <widget name="web_ribbon" text="归档" bg_color="bg-danger" attrs="{'invisible': [('active', '=', True)]}"/>
                <div class="oe_title" style="display: flex;align-items: center;">
                    <field name="image_1920" widget='image' class="oe_avatar" options='{"zoom": true, "preview_image":"image_128"}'/>
                    <h1 class="d-flex" style="margin: 0 20px;width: 400px;">
                        <field name="name" placeholder="名称" required="True"/>
                    </h1>
                    <div name="options" style="display: flex;">
                        <div>
                            <field name="customer"/>
                            <label for="customer"/>
                        </div>
                        <div>
                            <field name="supplier"/>
                            <label for="supplier"/>
                        </div>
                    </div>
                </div>
                <group id="g1">
                    <group>
                        <group>
                            <field name="code"/>
                            <field name="contacts_phone"/>
                            <field name="tag_ids" widget="many2many_tags" options="{'color_field': 'color', 'no_create_edit': True}" placeholder="点击给客户添加标签"/>
                        </group>
                        <group>
                            <field name="legal_person"/>
                            <field name="erp_id"/>
                            <field name="user_id"/>
                        </group>
                    </group>
                    <group>
                        <group>
                            <field name="contacts"/>
                            <field name="address"/>
                        </group>
                        <group>
                            <field name="vat_number"/>
                            <field name="active" widget="boolean_toggle"/>
                            <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}"/>
                        </group>
                    </group>
                </group>

                <notebook/>
                <group id="g2">
                    <field name="note" nolabel="1" placeholder="此处可以填写备注或描述" />
                </group>
                <div class="oe_chatter">
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>
    <!--action-->
    <record id="view_roke_customer_action" model="ir.actions.act_window">
        <field name="name">客户信息</field>
        <field name="res_model">roke.partner</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[('customer', '=', True)]</field>
        <field name="context">{'default_customer': True, 'default_supplier': False}</field>
        <field name="form_view_id" ref="view_roke_partner_form"/>
        <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
            点击左上角“创建”按钮新增一个客户。
          </p><p>
            或者您也可以选择批量导入功能一次性导入多个客户。
          </p>
        </field>
    </record>

    <!--action-->
    <record id="view_roke_supplier_action" model="ir.actions.act_window">
        <field name="name">供应商信息</field>
        <field name="res_model">roke.partner</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[('supplier', '=', True)]</field>
        <field name="context">{'default_customer': False, 'default_supplier': True}</field>
        <field name="form_view_id" ref="view_roke_partner_form"/>
        <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
            点击左上角“创建”按钮新增一个供应商。
          </p><p>
            或者您也可以选择批量导入功能一次性导入多个供应商。
          </p>
        </field>
    </record>

</odoo>
