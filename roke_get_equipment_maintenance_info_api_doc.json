{"openapi": "3.0.1", "info": {"title": "ROKE Equipment API", "description": "API documentation for ROKE Equipment module", "version": "1.0.0"}, "servers": [{"url": "http://localhost:8069", "description": "Local development server"}], "paths": {"/roke/get/equipment/maintenance_info": {"post": {"tags": ["Equipment"], "summary": "获取设备维护信息", "description": "获取设备点检、保养、维修、更换件记录，以及设备当天生产合格数", "operationId": "getEquipmentMaintenanceInfo", "requestBody": {"description": "Request parameters", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetEquipmentMaintenanceInfoRequest"}, "example": {"equipment_id": 1}}}, "required": false}, "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetEquipmentMaintenanceInfoResponse"}}}}}, "security": []}}}, "components": {"schemas": {"GetEquipmentMaintenanceInfoRequest": {"type": "object", "properties": {"equipment_id": {"type": "integer", "description": "设备ID，如果不提供则返回所有设备的维护信息"}}}, "GetEquipmentMaintenanceInfoResponse": {"type": "object", "properties": {"state": {"type": "string", "description": "请求状态，success表示成功，error表示失败", "enum": ["success", "error"]}, "msgs": {"type": "string", "description": "状态消息"}, "equipment_maintenance_list": {"type": "array", "description": "设备维护信息列表", "items": {"$ref": "#/components/schemas/EquipmentMaintenance"}}}}, "EquipmentMaintenance": {"type": "object", "properties": {"equipment_id": {"type": "integer", "description": "设备ID"}, "equipment_code": {"type": "string", "description": "设备编码"}, "equipment_name": {"type": "string", "description": "设备名称"}, "check_info": {"type": "object", "description": "点检信息", "properties": {"status": {"type": "string", "description": "点检状态", "enum": ["no_task", "not_started", "in_progress", "timeout", "finished"]}, "detail": {"type": "object", "description": "点检详细信息"}}}, "maintain_info": {"type": "object", "description": "保养信息", "properties": {"status": {"type": "string", "description": "保养状态", "enum": ["no_task", "not_started", "in_progress", "timeout", "finished"]}, "detail": {"type": "object", "description": "保养详细信息"}}}, "repair_info": {"type": "object", "description": "维修信息", "properties": {"status": {"type": "string", "description": "维修状态", "enum": ["no_task", "in_progress", "finished"]}, "detail": {"type": "object", "description": "维修详细信息"}}}, "change_info": {"type": "object", "description": "更换件信息", "properties": {"status": {"type": "string", "description": "更换件状态", "enum": ["no_task", "has_task"]}, "detail": {"type": "object", "description": "更换件详细信息"}}}, "finish_qty": {"type": "integer", "description": "当天生产合格数"}}}}}}