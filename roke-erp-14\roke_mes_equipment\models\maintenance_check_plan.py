# -*- coding: utf-8 -*-
from datetime import timedelta, time, datetime, date

from dateutil.relativedelta import relativedelta
from odoo import models, fields, api, _
from odoo.exceptions import UserError,ValidationError

import logging
_logger = logging.getLogger(__name__)

# from odoo.addons.roke_mes_equipment.models.spot_check_plan import  WEEKDAYS

class RokeMesEquipmentMaintenanceProject(models.Model):
    _name = "roke.mes.equipment.maintenance.project"
    _inherit = "roke.mes.equipment.common.inspection"

    user_id = fields.Many2one("res.users", string="指派人", required=True)
    repair_user_id = fields.Many2one("res.users", string="保养人" ,required=True)
    maintenance_scheme_id = fields.Many2one("roke.mes.maintenance.scheme", string="保养方案",required=True)

    order_ids = fields.One2many("roke.mes.maintenance.order", string="保养任务",compute="_compute_order_ids",readonly=True)

    @api.depends("maintenance_scheme_id", 'equipment_id')
    def _compute_order_ids(self):
        for rec in self:
            rec.order_ids = self.env['roke.mes.maintenance.order'].search([('origin_project_id', '=', rec.id)])

    @api.onchange('equipment_id')
    def _onchange_equipment_id(self):
        domain = {'maintenance_scheme_id': []}
        if self.equipment_id:
            domain = {'maintenance_scheme_id': [('equipment_ids', 'in', [self.equipment_id.id])]}
        return {"domain": domain}

    def action_view_maiantenance_order(self):
        tree = self.env.ref('roke_mes_equipment.view_roke_mes_maintenance_order_tree')
        form = self.env.ref('roke_mes_equipment.view_roke_mes_maintenance_order_form')
        return {
            'name': _('保养任务'),
            'view_mode': 'tree,form',
            'res_model': 'roke.mes.maintenance.order',
            'type': 'ir.actions.act_window',
            'domain': [('origin_project_id', '=', self.id)],
            'views': [(tree.id, 'tree'), (form.id, 'form')],
            'context': {'group_by': 'start_date:day' }
        }

    def _create_check_record(self, record, start_dt, end_dt):
        vals  ={
            "type": "maintain",
            "equipment_id": record.equipment_id.id,
            "maintenance_scheme_id": record.maintenance_scheme_id.id,
            "last_maintenance_date": fields.Date.today(),
            "user_id":record.user_id.id,
            'repair_user_id':record.user_id.id,
            'note':"自动保养任务",
            "estimated_completion_time": end_dt - timedelta(hours=8),
            'start_date': start_dt - timedelta(hours=8),
            'state':'wait',
            'priority':'normal',
            'origin_project_id':record.id,
            "item_ids":[
                (0,0,{
                    'item_id':item.id,
                    'maintenance_description':item.note,
                    'state':'wait',
                }) for item in record.maintenance_scheme_id.item_ids
            ],
        }
        res = self.env["roke.mes.maintenance.order"].create(vals)


    def execute_spot_check_record(self, res):
        """
        执行保养任务生成（精确到时分秒并按频率重复）
        """
        _logger.info("开始执行保养任务生成，记录ID: %s", res)
        records = self.browse(res)
        # current_datetime = fields.Datetime.now()
        current_datetime = fields.Datetime.now() + timedelta(hours=8)
        _logger.info("当前时间: %s", current_datetime)

        for record in records:
            _logger.info("处理保养计划 ID: %d，设备: %s", record.id, record.equipment_id.name)

            # 获取计划的时间设置（包含时分秒）
            start_dt = fields.Datetime.from_string(record.start_date + timedelta(hours=8))
            end_dt = fields.Datetime.from_string(record.end_date + timedelta(hours=8))
            _logger.info("计划时间范围: %s - %s", start_dt.time(), end_dt.time())
            existing = self.env["roke.mes.maintenance.order"].search([
                ("maintenance_scheme_id", "=", record.maintenance_scheme_id.id),
                ('origin_project_id', '=', record.id),
                ("equipment_id", "=", record.equipment_id.id),
                ("create_date", ">=", record.start_now_time - timedelta(hours=8)),
                ("create_date", "<=", record.end_now_time - timedelta(hours=8)),
            ], limit=1)

            if existing:
                _logger.info("该时间段内已存在记录，跳过创建")
                continue
            # 根据频率类型检查是否应该生成记录
            if record.frequency_type == 'daily':
                _logger.info("检查每日频率条件")
                if start_dt.time() <= current_datetime.time() <= end_dt.time():
                    _logger.info("满足每日保养条件，创建保养记录")
                    self._create_check_record(record, record.start_now_time, record.end_now_time)

            elif record.frequency_type == 'weekly':
                _logger.info("检查每周频率条件")
                if (start_dt.weekday() == current_datetime.weekday() and
                        start_dt.time() <= current_datetime.time() <= end_dt.time()):
                    _logger.info("满足每周保养条件，创建保养记录")
                    self._create_check_record(record, record.start_now_time, record.end_now_time)

            elif record.frequency_type == 'monthly':
                _logger.info("检查每月频率条件")
                if (start_dt.day == current_datetime.day and
                        start_dt.time() <= current_datetime.time() <= end_dt.time()):
                    _logger.info("满足每月保养条件，创建保养记录")
                    self._create_check_record(record, record.start_now_time, record.end_now_time)
    def write(self, vals):
        res = super(RokeMesEquipmentMaintenanceProject, self).write(vals)
        if any(field in vals for field in ['frequency_type', 'maintenance_scheme_id', 'equipment_id','start_date','active']):
            self.update_ir_cron(type="保养")
        return res

    @api.model
    def create(self, vals):
        vals["code"] = self.env['ir.sequence'].next_by_code('roke.mes.equipment.maintenance.project.code')
        record = super(RokeMesEquipmentMaintenanceProject, self).create(vals)
        record.update_ir_cron()
        return record

