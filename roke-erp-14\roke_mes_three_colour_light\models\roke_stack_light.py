from odoo import api, fields, models, _
import requests, json, logging
from odoo.exceptions import UserError

_logger = logging.getLogger(__name__)


class RokeStackLight(models.Model):
    _name = "roke.stack.light"
    _description = "安灯"

    code = fields.Char(string="编号", size=30)
    plant_id = fields.Many2one('roke.plant', string='车间')
    state = fields.Selection(string="状态", selection=[("正常", "正常"), ("异常", "异常")], compute="_compute_state", store=True)
    alarm_ids = fields.One2many(string="安灯异常", comodel_name="roke.abnormal.alarm", inverse_name="light_id")
    is_bind = fields.Boolean(string="是否绑定", default=False)

    def bind_remote_stack_light(self):
        """绑定远程硬件安灯"""
        for item in self:
            try:
                database_uuid = self.env['ir.config_parameter'].sudo(
                ).get_param("database.uuid", '-1')
                web_base_url = self.env['ir.config_parameter'].sudo(
                ).get_param("web.base.url", '-1')
                api_path = 'https://dws-platform.xbg.rokeris.com/dev-api/public/device/remote'
                payload = {
                    "remote_code": database_uuid, 
                    "remote_name": self.env.user.company_id.name,
                    "device_code": item.code,
                    "remote_url": web_base_url
                }
                _logger.info(f"请求物联网平台接口：{api_path}")
                _logger.info(f"请求物联网平台接口参数：{payload}")
                res = requests.post(api_path, data=json.dumps(payload), headers={"Content-Type": "application/json"},)
                res_result = json.loads(res.text)
                if res_result["code"] == 200:
                    item.write({"is_bind": True})
                else:
                    raise UserError(res_result["msg"])
            except Exception as e:
                raise UserError(f"绑定失败：{e}")


    def action_open_info(self):
        tree_id = self.env.ref("roke_mes_three_colour_light.view_stack_light_roke_abnormal_alarm_tree").id
        return {
            'name': "安灯异常",
            'type': 'ir.actions.act_window',
            'view_mode': 'tree',
            'target': 'new',
            'views': [[tree_id, 'tree']],
            'res_model': 'roke.abnormal.alarm',
            'domain': [('light_id', '=', self.id)]
        }

    @api.depends("alarm_ids.abnormal_status")
    def _compute_state(self):
        for item in self:
            if item.alarm_ids.filtered(lambda x: x.abnormal_status != "处理完成"):
                item.state = "异常" 
            else:
                item.state = "正常"


class RokeStackLightConfig(models.Model):
    _name = "roke.stack.light.config"
    _description = "安灯配置"

    name = fields.Many2one(string="异常类型", comodel_name="roke.abnormal.alarm.type")
    color = fields.Selection([
        ('red', '红'),
        ('yellow', '黄'),
        ('green', '绿'),
        ('blue', '蓝'),
        ('gray', '灰')
    ], string="颜色", required=True)
    circle_html = fields.Html(string="显示", compute="_compute_circle_html", sanitize=False)
    is_notify = fields.Selection([("是", "是"), ("否", "否")], string="是否发送通知", default="否")
    notify_group_id = fields.Many2one("res.groups", string="通知用户组")
    notify_user_ids = fields.Many2many("res.users", string="通知用户")
    mini_program = fields.Boolean(string="小程序")
    phone = fields.Boolean(string="电话")
    wechat = fields.Boolean(string="企业微信")
    dingding = fields.Boolean(string="钉钉")
    sms = fields.Boolean(string="短信")
    screen = fields.Boolean(string="大屏看板")
    email = fields.Boolean(string="邮件")
    roke_app = fields.Boolean(string="融科app")
    response_time = fields.Float(string="响应时间（分钟）")
    repeat_interval = fields.Float(string="重复间隔（分钟）")
    backup_group_id = fields.Many2one("res.groups", string="备用通知用户组")
    close_user_id = fields.Many2one("res.users", string="关闭人")
    close_by_create_user = fields.Boolean(string="发起人")

    @api.onchange("notify_group_id")
    def _onchange_notify_group_id(self):
        domain = []
        if self.notify_group_id:
            domain = [("groups_id", "in", self.notify_group_id.id)]
        return {"domain": {"notify_user_ids": domain}}

    @api.model
    def get_stack_light_state(self):
        config_id = self.search([('name', '=', '设备异常')], limit=1)
        if not config_id:
            return
        factory_code = self.env['ir.config_parameter'].get_param('database.uuid', default="")
        try:
            res = requests.post(
                url="https://dws-platform.xbg.rokeris.com/dev-api/public/device_efficiency/device_state_list",
                data=json.dumps({'factory_code': factory_code}),
                headers={"Content-Type": "application/json"}
            )
            res_json = json.loads(res.text)
        except Exception as e:
            print("异常：", e)
            return
        if res_json.get("code") != 200:
            return
        _logger.error(f"获取到的设备状态数据：{res_json.get('data', '')}")
        for item in res_json.get("data", []):
            if item.get("state") == config_id.color:
                equipment_id = self.env["roke.mes.equipment"].search([
                    ("code", "=", item.get("code"))
                ], limit=1)
                if not equipment_id:
                    continue
                abnormal_alarm_id = self.env["roke.abnormal.alarm"].search([
                    ("equipment_id", "=", equipment_id.id),
                    ("alarm_state", "!=", "关闭"),
                    ("abnormal_id", "=", config_id.name.id)
                ], limit=1)
                if abnormal_alarm_id:
                    continue
                self.env["roke.abnormal.alarm"].create({
                    "plant_id": equipment_id.plant_id.id,
                    "workshop_id": equipment_id.workshop_id.id,
                    "work_center": equipment_id.work_center_id.id,
                    "abnormal_id": config_id.name.id,
                    "alarm_state": "开始",
                    # "sponsor": self.env.user.id,
                    "originating_time": fields.Datetime.now(),
                })

    @api.depends('color')
    def _compute_circle_html(self):
        for rec in self:
            if rec.color:
                rec.circle_html = f"""
                    <span style="
                        display:inline-block;
                        width:26px;
                        height:26px;
                        border-radius:50%;
                        background:{rec.color};
                        border: 1px solid #999;
                    "></span>
                """
            else:
                rec.circle_html = ""
