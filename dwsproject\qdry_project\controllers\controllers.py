import os
import datetime
import logging
import requests
from odoo.addons.roke_mes_client.controller import login as mes_login
from odoo import http, tools, SUPERUSER_ID
from jinja2 import FileSystemLoader, Environment
import pytz
from dateutil.relativedelta import relativedelta
_logger = logging.getLogger(__name__)

BASE_DIR = os.path.dirname(os.path.dirname(__file__))
templateloader = FileSystemLoader(searchpath=BASE_DIR + "/static/src/view")
env = Environment(loader=templateloader)


class RokeMesThreeColourLight(http.Controller):
    @http.route("/roke/three_color_light/device_state_list", type="http", auth='none', cors='*', csrf=False)
    def device_state_list(self, **kwargs):
        _self = http.request
        factory_code = http.request.env(user=SUPERUSER_ID)['ir.config_parameter'].get_param('database.uuid', default="")
        data = {"code": 1, "message": "请求通过", "data": {"factory_code": factory_code}}
        template = env.get_template('equipment_status_qdry.html')
        return template.render(data)