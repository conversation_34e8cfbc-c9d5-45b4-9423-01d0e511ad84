# -*- coding: utf-8 -*-
"""
Description:
    设备维修保养
Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
from odoo import models, fields, api, _
from odoo.exceptions import UserError
import datetime
import logging
_logger = logging.getLogger(__name__)


class RokeMesMaintenanceItem(models.Model):
    _name = "roke.mes.maintenance.item"
    _description = "保养项目"

    code = fields.Char(string="编号", default=lambda self: self.env['ir.sequence'].next_by_code('roke.mes.maintain.item.code'))
    name = fields.Char(string="名称")
    note = fields.Text(string="保养内容")
    company_id = fields.Many2one('res.company', string='公司', default=lambda self: self.env.company)


class RokeMesMaintenanceScheme(models.Model):
    _name = "roke.mes.maintenance.scheme"
    _description = "保养方案"
    _inherit = ['mail.thread']
    _order = "next_maintenance_date, code"

    name = fields.Char(string="方案说明")
    code = fields.Char(string="编号", default=lambda self: self.env['ir.sequence'].next_by_code('roke.mes.maintain.scheme.code'))
    active = fields.Boolean(string="是否有效", default=True)
    equipment_ids = fields.Many2many("roke.mes.equipment", string="适用设备")
    item_ids = fields.Many2many("roke.mes.maintenance.item", string="保养项目")
    # 保养频率
    # 一年/5000KM。N年/月/周/天。生产N件产品后
    frequency = fields.Integer(string="保养频率", default=1)
    frequency_unit = fields.Selection([("年", "年"), ("月", "月"), ("周", "周"), ("日", "日")], string="保养频率单位", default="年")
    show_frequency = fields.Char(string="保养频率", compute="_compute_show_frequency")
    frequency_days = fields.Integer(string="保养频率（天）", compute="_compute_frequency_days", store=True)
    next_maintenance_date = fields.Date(string="下次保养日期", compute="_compute_next_maintenance_date", store=True)
    # 历史保养记录
    last_maintenance_date = fields.Date(string="上次保养日期")
    maintenance_order_ids = fields.One2many("roke.mes.maintenance.order", "maintenance_scheme_id", string="保养记录")
    company_id = fields.Many2one('res.company', string='公司', default=lambda self: self.env.company)
    ir_cron = fields.Many2one("ir.cron", string="自动动作")

    # 创建保养任务
    def equipment_id_holiday(self, equipment_id):
        arrangements = self.sudo().env['roke.equipment.holiday.arrangement'].search(
            [('equipment_ids', 'in', equipment_id.id)])
        tag = True
        for arr in arrangements:
            date = arr.line_ids.filtered(
                lambda
                    d: d.start_date <= datetime.datetime.today().date() and datetime.datetime.today().date() <= d.end_date)
            if date:
                tag = False
                break
        return tag

    # 创建保养任务
    def create_maintenance_order(self, res):
        scheme_id = self.sudo().env['roke.mes.maintenance.scheme'].browse(int(res))
        for equipment_id in scheme_id.equipment_ids:
            if equipment_id.e_state not in ('waiting', 'stop') and equipment_id.active:
                tag = self.equipment_id_holiday(equipment_id)
                if tag:
                    lines = []
                    for line in scheme_id.item_ids:
                        lines.append((0, 0, {
                            'item_id': line.id
                        }))
                    self.sudo().env['roke.mes.maintenance.order'].create({
                        'type': 'maintain',
                        'equipment_id': equipment_id.id,
                        'state': 'assign',
                        'maintenance_scheme_id': scheme_id.id,
                        'item_ids': lines
                    })

    # 获取时间标识
    def get_interval_type(self, res):
        if res.frequency_unit == '年':
            interval_number = res.frequency * 12
            interval_type = 'months'
        elif res.frequency_unit == '月':
            interval_number = res.frequency
            interval_type = 'months'
        elif res.frequency_unit == '周':
            interval_number = res.frequency
            interval_type = 'weeks'
        else:
            interval_number = res.frequency
            interval_type = 'days'
        return interval_number, interval_type

    @api.model
    def create(self, vals):
        res = super(RokeMesMaintenanceScheme, self).create(vals)
        # # 自动生成定时任务
        # interval_number, interval_type = self.get_interval_type(res)
        # cron_id = self.sudo().env['ir.cron'].create({
        #     'name': '设备保养方案[名称：%s，周期：%s]' % (res.name, str(res.frequency) + res.frequency_unit),
        #     'model_id': self.sudo().env['ir.model'].search([('model', '=', 'roke.mes.maintenance.scheme')],
        #                                                    limit=1).id,
        #     'state': 'code',
        #     'code': 'model.create_maintenance_order(%s)' % str(res.id),
        #     'interval_number': interval_number,
        #     'interval_type': interval_type,
        #     'numbercall': -1,
        #     'doall': True,
        #     'active': True
        # })
        # res.ir_cron = cron_id.id
        return res

    def unlink(self):
        # # 同步删除定时任务
        # for record in self:
        #     if record.ir_cron:
        #         record.ir_cron.unlink()
        res = super(RokeMesMaintenanceScheme, self).unlink()
        return res

    def write(self, vals):
        res = super(RokeMesMaintenanceScheme, self).write(vals)
        _logger.info('修改保存时上次:{}'.format(self.last_maintenance_date))
        _logger.info('修改保存时下次:{}'.format(self.next_maintenance_date))
        _logger.info(self.frequency_days)
        # self.update_ir_cron()
        return res

    def update_ir_cron(self):
        for record in self:
            interval_number, interval_type = self.get_interval_type(record)
            if record.ir_cron:
                record.ir_cron.write({
                    'name': '设备保养方案[名称：%s，周期：%s]' % (
                        record.name, str(record.frequency) + record.frequency_unit),
                    'interval_number': interval_number,
                    'interval_type': interval_type
                })
            else:
                cron_id = self.sudo().env['ir.cron'].create({
                    'name': '设备保养方案[名称：%s，周期：%s]' % (
                        record.name, str(record.frequency) + record.frequency_unit),
                    'model_id': self.sudo().env['ir.model'].search(
                        [('model', '=', 'roke.mes.maintenance.scheme')], limit=1).id,
                    'state': 'code',
                    'code': 'model.create_maintenance_order(%s)' % str(record.id),
                    'interval_number': interval_number,
                    'interval_type': interval_type,
                    'numbercall': -1,
                    'doall': True,
                    'active': True
                })
                record.ir_cron = cron_id.id

    def _compute_show_frequency(self):
        for record in self:
            record.show_frequency = "%s%s" % (str(record.frequency), record.frequency_unit)

    @api.depends("last_maintenance_date", "frequency_days", "ir_cron", "ir_cron.nextcall")
    def _compute_next_maintenance_date(self):
        # 计算下次保养日期
        for record in self:
            if record.ir_cron:
                record.next_maintenance_date = (record.ir_cron.nextcall + datetime.timedelta(hours=8)).date()
            else:
                record.next_maintenance_date = (datetime.datetime.now() + datetime.timedelta(hours=8)).date()

    @api.depends("frequency_unit", "frequency")
    def _compute_frequency_days(self):
        # 计算保养频率间隔天数
        for record in self:
            if record.frequency_unit == "年":
                unit = 365
            elif record.frequency_unit == "月":
                unit = 30
            elif record.frequency_unit == "周":
                unit = 7
            else:
                unit = 1
            record.frequency_days = record.frequency * unit

    def execute_scheme(self):
        # 执行保养方案生成保养任务单
        create_list = []
        for record in self:
            for equipment in record.equipment_ids:
                items = []
                for item in record.item_ids:
                    items.append((0, 0, {'item_id': item.id}))
                create_list.append({
                    "type": "maintain",
                    "equipment_id": equipment.id,
                    "maintenance_scheme_id": record.id,
                    "last_maintenance_date": fields.Date.today(),
                    'item_ids': items
                })
            # 更新下次保养时间
            record.next_maintenance_date = fields.Date.today() + datetime.timedelta(days=record.frequency_days)
            record.last_maintenance_date = fields.Date.today()
        self.env["roke.mes.maintenance.order"].create(create_list)

    def check_scheme_date(self):
        # 校验是否生成保养单
        today = fields.Date.context_today(self)
        schemes = self.search([("next_maintenance_date", "<=", today)]).execute_scheme()


class RokeEquipmentHolidayArrangement(models.Model):
    _name = "roke.equipment.holiday.arrangement"
    _description = "节假日安排"
    _rec_name = "code"

    code = fields.Char(string="变更编号", required=True, index=True, copy=False, default="保存自动生成编号")
    equipment_ids = fields.Many2many("roke.mes.equipment", string="适用设备")
    department_id = fields.Many2one('roke.department', string="部门")

    line_ids = fields.One2many("roke.equipment.holiday.arrangement.line", "line_id", string="检验数据记录")

    @api.model
    def create(self, vals):
        """
        自动生成编号
        """
        if not vals.get('code') or vals.get('code') in ["保存自动生成编号", ""]:
            code = self.env['ir.sequence'].next_by_code('roke.equipment.holiday.arrangement.code')
            vals["code"] = code
        return super(RokeEquipmentHolidayArrangement, self).create(vals)


class RokeEquipmentHolidayArrangementLine(models.Model):
    _name = "roke.equipment.holiday.arrangement.line"
    _description = "节假明细"

    line_id = fields.Many2one('roke.equipment.holiday.arrangement', string="节假日安排", required=True,
                              ondelete='cascade')
    start_date = fields.Date(string="开始日期", default=fields.Date.today())
    end_date = fields.Date(string="结束日期", default=fields.Date.today())
