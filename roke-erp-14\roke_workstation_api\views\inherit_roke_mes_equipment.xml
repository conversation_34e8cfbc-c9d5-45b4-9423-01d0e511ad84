<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--search-->
    <record id="dws_inherit_view_roke_mes_equipment_search" model="ir.ui.view">
        <field name="name">dws.inherit.roke.mes.equipment.search</field>
        <field name="model">roke.mes.equipment</field>
        <field name="inherit_id" ref="roke_mes_equipment.view_roke_mes_equipment_search"/>
        <field name="arch" type="xml">
            <xpath expr="//search" position="replace">
                <search string="设备">
                    <field string="设备" name="name" filter_domain="['|', ('name', 'ilike', self), ('code', 'ilike', self)]"/>
                    <field name="work_center_id" string="绑定工位"/>
                    <field name="workshop_id"/>
                    <field name="plant_id"/>
                    <group expand="1" string="Group By">
                        <filter string="绑定车间" name="plant_id" context="{'group_by':'plant_id'}"/>
                        <filter string="绑定产线" name="workshop_id" context="{'group_by':'workshop_id'}"/>
                    </group>
                    <searchpanel>
                        <field name="plant_id" icon="fa-wrench" enable_counters="1" expand="1"/>
                        <field name="workshop_id" icon="fa-wrench" enable_counters="1" expand="1"/>
                    </searchpanel>
                </search>
            </xpath>
        </field>
    </record>

    <record id="view_dws_inherit_roke_mes_equipment_form" model="ir.ui.view">
        <field name="name">view_dws_inherit_roke_mes_equipment_form</field>
        <field name="model">roke.mes.equipment</field>
        <field name="inherit_id" ref="roke_mes_equipment.view_roke_mes_equipment_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='specification']" position="after">
                <field name="plant_id"/>
            </xpath>
            <xpath expr="//field[@name='manufacture_date']" position="after">
                <field name="workshop_id" attrs="{'readonly': [('plant_id', '=', False)]}"/>
            </xpath>
            <xpath expr="//field[@name='active_date']" position="after">
                <field name="work_center_id" string="工位" attrs="{'readonly': [('workshop_id', '=', False)]}"/>
            </xpath>
        </field>
    </record>

    <record id="dws_inherit_view_roke_mes_equipment_kanban" model="ir.ui.view">
        <field name="name">roke.mes.equipment.kanban</field>
        <field name="model">roke.mes.equipment</field>
        <field name="arch" type="xml">
            <kanban class="o_kanban_view o_kanban_mobile" js_class="WSKanbanView">
                <field name="work_center_id"/>
                <field name="id"/>
                <field name="code"/>
                <field name="name"/>
                <field name="e_state"/>
                <field name="light_state"/>
                <templates>
                    <t t-name="kanban-box">
                        <div name="roke_equipment_box" style="display: flex; flex-direction: row; flex-wrap: nowrap;">
                            <div style="flex: 1;">
                                <div style="border-left: 5px solid #3a5268; padding-left: 10px; font-size: 15px; margin-bottom: 10px;">
                                    <t t-if="record.work_center_id.raw_value">
                                        <field name="work_center_id"/>
                                    </t>
                                    <t t-else="">
                                        <span>未绑定工位</span>
                                    </t>
                                </div>
                                <div style="font-size: 15px;">
                                    <span>设备名称</span>
                                    <field name="name"/>
                                </div>
                            </div>

                            <div style="flex: 1;" t-attf-class="text-right light_{{record.code.value}}_card">
                                <field name="light_state"/>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>


    <record id="roke_mes_equipment.view_roke_mes_equipment_action" model="ir.actions.act_window">
        <field name="view_mode">tree,form,kanban,pivot</field>
    </record>

    <record id="view_roke_equipment_light_config_form" model="ir.ui.view">
        <field name="name">roke.equipment.light.config.form</field>
        <field name="model">roke.equipment.light.config</field>
        <field name="arch" type="xml">
            <form string="三色灯配置" create="0" delete="0" duplicate='0' import="0" form_import="0">
                <div class="mb16" style="display: flex; flex-direction: row; align-items: center;">
                    <span style="background: green; border: 1px solid #afafaf; width: 16px; height: 16px; border-radius: 50%"></span>
                    <span class="ml16">代表</span>
                    <div class="ml16" style="width: 250px">
                        <field name="label_green"/>
                    </div>
                    <span class="ml16">是否需要通知</span>
                    <div class="ml16" style="width: 250px">
                        <field name="is_green_notify" widget="boolean_toggle"/>
                    </div>
                </div>
                <div class="mb16" style="display: flex; flex-direction: row; align-items: center;">
                    <span style="background: yellow; border: 1px solid #afafaf; width: 16px; height: 16px; border-radius: 50%"></span>
                    <span class="ml16">代表</span>
                    <div class="ml16" style="width: 250px">
                        <field name="label_yellow"/>
                    </div>
                    <span class="ml16">是否需要通知</span>
                    <div class="ml16" style="width: 250px">
                        <field name="is_yellow_notify" widget="boolean_toggle"/>
                    </div>
                </div>
                <div class="mb16" style="display: flex; flex-direction: row; align-items: center;">
                    <span style="background: red; border: 1px solid #afafaf; width: 16px; height: 16px; border-radius: 50%"></span>
                    <span class="ml16">代表</span>
                    <div class="ml16" style="width: 250px">
                        <field name="label_red"/>
                    </div>
                    <span class="ml16">是否需要通知</span>
                    <div class="ml16" style="width: 250px">
                        <field name="is_red_notify" widget="boolean_toggle"/>
                    </div>
                </div>
                <div class="mb16" style="display: flex; flex-direction: row; align-items: center;">
                    <span style="background: gray; border: 1px solid #afafaf; width: 16px; height: 16px; border-radius: 50%"></span>
                    <span class="ml16">代表</span>
                    <div class="ml16" style="width: 250px">
                        <field name="label_gray"/>
                    </div>
                    <span class="ml16">是否需要通知</span>
                    <div class="ml16" style="width: 250px">
                        <field name="is_gray_notify" widget="boolean_toggle"/>
                    </div>
                </div>
            </form>
        </field>
    </record>

    <record id="view_roke_equipment_light_config_tree" model="ir.ui.view">
        <field name="name">roke.equipment.light.config.tree</field>
        <field name="model">roke.equipment.light.config</field>
        <field name="arch" type="xml">
            <tree string="三色灯配置" create="0" delete="0" duplicate='0' import="0">
                <field name="label_green"/>
                <field name="label_yellow"/>
                <field name="label_red"/>
                <field name="label_gray"/>
            </tree>
        </field>
    </record>

    <record id="action_roke_equipment_light_config" model="ir.actions.act_window">
        <field name="name">三色灯配置</field>
        <field name="res_model">roke.equipment.light.config</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{}</field>
    </record>
<!--    <record id="action_roke_equipment_status" model="ir.actions.client">-->
<!--        <field name="name">异常统计</field>-->
<!--        <field name="tag">roke_workstation_api.roke_equipment_status</field>-->
<!--        <field name="target">current</field>-->
<!--    </record>-->
    <record id="action_roke_equipment_status_url" model="ir.actions.act_url">
        <field name="name">异常统计</field>
        <field name="url">/roke/equipment/status</field>
        <field name="target">new</field>
    </record>

    <record id="action_abnormal_alarm_census" model="ir.actions.client">
        <field name="name">安灯数据</field>
        <field name="tag">roke_workstation_api.abnormal_alarm</field>
        <field name="target">current</field>
    </record>

    <record id="action_work_order_staticfy" model="ir.actions.client">
        <field name="name">工单统计看板</field>
        <field name="tag">roke_workstation_api.work_order_staticfy</field>
        <field name="target">current</field>
    </record>

    <record id="action_equipment_staticfy" model="ir.actions.client">
        <field name="name">设备统计看板</field>
        <field name="tag">roke_workstation_api.equipment_staticfy</field>
        <field name="target">current</field>
    </record>

   <record id="action_energy_staticfy" model="ir.actions.client">
        <field name="name">能耗统计看板</field>
        <field name="tag">roke_workstation_api.energy_staticfy</field>
        <field name="target">current</field>
    </record>

    <menuitem id="roke_equipment_light_config_menu" name="三色灯配置" parent="roke_mes_equipment.roke_mes_equipment_manage_menu" action="action_roke_equipment_light_config" sequence="30" groups="base.group_system"/>
<!--    <menuitem id="roke_status_menu" name="异常统计" parent="roke_mes_equipment.roke_mes_equipment_manage_menu" action="action_roke_equipment_status" sequence="30" groups="base.group_system"/>-->
    <menuitem id="roke_status_menu" name="异常统计" parent="roke_mes_production.roke_kanban_main_menu" action="action_roke_equipment_status_url" sequence="30" groups="base.group_system"/>
    <menuitem id="menu_abnormal_alarm_census" name="安灯数据" parent="roke_mes_equipment.roke_mes_equipment_manage_menu" action="action_abnormal_alarm_census" sequence="31" groups="base.group_system"/>

    <menuitem id="roke_work_order_staticfy_menu" name="工单统计看板" parent="roke_mes_production.roke_kanban_main_menu" action="action_work_order_staticfy" sequence="40" groups="base.group_system"/>
    <menuitem id="roke_equipment_staticfy_menu" name="设备统计看板" parent="roke_mes_production.roke_kanban_main_menu" action="action_equipment_staticfy" sequence="50" groups="base.group_system"/>
    <menuitem id="roke_energy_staticfy_menu" name="能耗统计看板" parent="roke_mes_production.roke_kanban_main_menu" action="action_energy_staticfy" sequence="60" groups="base.group_system"/>

</odoo>