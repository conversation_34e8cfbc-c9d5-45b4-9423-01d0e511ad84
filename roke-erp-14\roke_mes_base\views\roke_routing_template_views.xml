<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--工艺路线模板-->
    <!--search-->
    <record id="view_roke_routing_template_search" model="ir.ui.view">
        <field name="name">roke.routing.template.search</field>
        <field name="model">roke.routing.template</field>
        <field name="arch" type="xml">
            <search string="工艺路线模板">
                <field name="name"/>
                <field name="process_description"/>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_routing_template_tree" model="ir.ui.view">
        <field name="name">roke.routing.template.tree</field>
        <field name="model">roke.routing.template</field>
        <field name="arch" type="xml">
            <tree string="工艺路线模板">
                <field name="name"/>
                <field name="remark"/>
                <field name="process_description"/>
                <field name="create_uid" string="创建人" optional="show"/>
                <field name="create_date" string="创建时间" optional="show"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_routing_template_form" model="ir.ui.view">
        <field name="name">roke.routing.template.form</field>
        <field name="model">roke.routing.template</field>
        <field name="arch" type="xml">
            <form string="工艺路线模板">
                <widget name="web_ribbon" text="归档" bg_color="bg-danger"
                            attrs="{'invisible': [('active', '=', True)]}"/>
                <group col="8">
                    <field name="name" placeholder="名称" required="1"/>
                    <field name="process_description"/>
                    <field name="active" invisible="1"/>
                </group>
                <notebook>
                    <page string="工序明细">
                        <button name="multi_add_routing_action" string="批量添加工序" type="object" class="oe_highlight"
                                attrs="{'invisible': [('active', '=', False)]}"/>
                        <field name="line_ids" create="0">
                            <tree editable="bottom">
                                <field name="sequence"/>
                                <field name="process_id"/>
                                <field name="remark"/>
                            </tree>
                        </field>
                    </page>
                </notebook>
                <group>
                    <field name="remark"/>
                </group>
                <div class="oe_chatter">
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>
    <!--action-->
    <record id="view_roke_routing_template_action" model="ir.actions.act_window">
        <field name="name">工艺路线模板</field>
        <field name="res_model">roke.routing.template</field>
        <field name="view_mode">tree,form</field>
        <field name="form_view_id" ref="view_roke_routing_template_form"/>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{}</field>
    </record>

</odoo>
