<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--产品信息-->
    <!--search-->
    <record id="view_roke_product_search" model="ir.ui.view">
        <field name="name">roke.product.search</field>
        <field name="model">roke.product</field>
        <field name="arch" type="xml">
            <search string="产品信息">
                <field name="name"/>
                <field name="code"/>
                <field name="specification"/>
                <field name="model"/>
                <field name="product_features"/>
                <field name="category_id"/>
                <field name="routing_id"/>
                <filter string="已归档" name="inactive" domain="[('active', '=', False)]"/>
                <filter string="半成品" name="half_product" domain="[('type', '=', '半成品')]"/>
                <filter string="产成品" name="product" domain="[('type', '=', '产成品')]"/>
                <filter string="原材料" name="original" domain="[('type', '=', '原材料')]"/>
                <filter string="其他" name="other" domain="[('type', '=', '其他')]"/>
                <group expand="0" string="Group By">
                    <filter string="产品类别" name="group_category_id" context="{'group_by': 'category_id'}"/>
                </group>
                <searchpanel>
                    <field name="category_id" icon="fa-cubes" limit="2000" enable_counters="1" expand="1"/>
                </searchpanel>
            </search>
        </field>
    </record>
    <!--tree-->
    <!-- 2024-09-26 于大伟 workstation后台，产品信息视图改造 -->
    <record id="view_roke_product_tree" model="ir.ui.view">
        <field name="name">roke.product.tree</field>
        <field name="model">roke.product</field>
        <field name="arch" type="xml">
            <tree string="产品信息">
                <field name="code"/>
                <field name="name"/>
                <field name="specification"/>
                <!-- <field name="model"/> -->
                <!-- <field name="product_features" /> -->
                <field name="uom_id"/>
                <field name="routing_id" optional="show"/>
                <!-- <field name="spoiled_rates"/> -->
                <!-- <field name="category_id"/> -->
                <field name="note" optional="hide"/>
                <field name="create_uid" string="创建人" optional="show"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_product_form" model="ir.ui.view">
        <field name="name">roke.product.form</field>
        <field name="model">roke.product</field>
        <field name="arch" type="xml">
            <form string="产品信息">
                <div name="button_box" class="oe_button_box"/>
                <widget name="web_ribbon" text="归档" bg_color="bg-danger" attrs="{'invisible': [('active', '=', True)]}"/>
                <div style="display: flex;align-items: end;">
                    <field name="image_1920" widget='image' class="oe_avatar" options='{"zoom": true, "preview_image":"image_128"}'/>
                    <div style="margin: 0 10px;">
                        <label for="name_repeat" class="oe_edit_only" decoration-danger="True" style="color:red" attrs="{'invisible': [('name_repeat', '!=', True)]}"/>
                        <h1 class="d-flex">
                            <field name="name" placeholder="产品名称" required="True"/>
                            <field name="name_repeat" invisible="1"/>
                        </h1>
                        <!-- <label for="code" class="oe_edit_only"/> -->
                        <h3 class="d-flex">
                            <field name="code" placeholder="产品编号" required="True"/>
                        </h3>
                    </div>
                    <div>
                        <div class="oe_title"></div>
                        <div name="options" style="width: 400px;">
                            <div class="o_row">
                                <field name="type" widget="selection_badge" readonly="0"/>
                            </div>
                        </div>
                    </div>
                </div>
                <notebook>
                    <page string="基础信息">
                        <group string="基础" name="base" col="4">
                            <group>
                                <field name="category_id"/>
                                <field name="spoiled_rates"/>
                                <field name="active" widget="boolean_toggle"/>
                            </group>
                            <group>
                                <field name="specification"/>
                                <field name="gross_profit_rate"/>
                                <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}"/>
                            </group>
                            <group>
                                <field name="model"/>
                                <field name="erp_id"/>
                                <field name="standard_weight"/>
                            </group>
                            <group>
                                <field name="product_features"/>
                                <field name="uom_id" required="1"/>
                            </group>
<!--                                <group>-->
<!--                                    <field name="category_id"/>-->
<!--                                    <field name="specification"/>-->
<!--                                    <field name="model"/>-->
<!--                                    <field name="product_features"/>-->
<!--                                    <field name="spoiled_rates"/>-->
<!--                                    <field name="gross_profit_rate"/>-->
<!--                                </group>-->
<!--                                <group>-->
<!--                                    <field name="erp_id"/>-->
<!--                                    <field name="uom_id" required="1"/>-->
<!--                                    <field name="active" widget="boolean_toggle"/>-->
<!--                                    <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}"/>-->
<!--                                    <field name="standard_weight"/>-->
<!--                                </group>-->
                        </group>
                        <group string="生产" name="production">
                            <group name="production_1">
                                <group>
                                    <field name="without_wo_process_ids" options="{'no_create': True}"
                                       widget="many2many_tags"/>
                                    <field name="routing_id" domain="[('state', '=', '确认')]"/>
                                </group>
                                <group>
                                    <field name="work_center_ids" options="{'no_create': True}"
                                       widget="many2many_tags"/>
                                </group>
                            </group>
                            <group name="production_2">
                            </group>
                        </group>
                    </page>
                    <page string="作业规范" name="standard_item">
                        <field name="standard_item_ids"
                               context="{'tree_view_ref': 'roke_mes_base.view_roke_work_standard_item_editable_tree'}"/>
                    </page>
                    <page string="成本">
                        <group string="定额成本" name="fixed_cost">
                            <group>
                                <field name="direct_material_price"/>
                                <field name="direct_labor_price"/>
                                <field name="manufacturing_costs"/>
                            </group>

                        </group>

                    </page>
                </notebook>
                <group id="g2">
                    <field name="note" nolabel="1" placeholder="此处可以填写备注或描述" />
                </group>
                <div class="oe_chatter">
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>
    <!--action-->
    <record id="view_roke_product_action" model="ir.actions.act_window">
        <field name="name">产品信息</field>
        <field name="res_model">roke.product</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{}</field>
        <field name="form_view_id" ref="view_roke_product_form"/>
        <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
            点击左上角“创建”按钮新增一个产品。
          </p><p>
            或者您也可以选择批量导入功能一次性导入多个产品。
          </p>
        </field>
    </record>

</odoo>
