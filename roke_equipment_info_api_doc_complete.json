{"openapi": "3.0.1", "info": {"title": "ROKE Equipment API", "description": "API documentation for ROKE Equipment module", "version": "1.0.0"}, "servers": [{"url": "http://localhost:8069", "description": "Local development server"}], "paths": {"/roke/equipment/info": {"post": {"tags": ["Equipment"], "summary": "获取设备基础信息", "description": "获取设备的详细信息，包括基本信息、维修记录、保养记录、点检记录和更换件记录（物联网灯专用）", "operationId": "getEquipmentInfo", "requestBody": {"description": "Request parameters", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetEquipmentInfoRequest"}, "example": {"equipment_id": 1}}}, "required": true}, "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetEquipmentInfoResponse"}, "example": {"state": "success", "msgs": "获取成功", "equipment": {"id": 1, "name": "生产设备A", "code": "EQ001", "category": "生产设备", "specification": "型号A-001", "e_state": "在用", "plant_name": "第一车间", "workshop_name": "生产线A", "work_center_name": "工位001", "user_name": "张三", "manufacture_date": "2023-01-15", "warranty_date": "2025-01-15", "repair_record_list": [{"id": 1, "report_user_id": 2, "report_user_name": "李四", "repair_user_id": 3, "repair_user_name": "王五", "report_time": "2023-06-01", "fault_description": "设备异响", "state": "已完成", "maintenance_scheme": "更换轴承", "finish_time": "2023-06-01 14:30:00", "equipment_name": "生产设备A", "priority": "高", "last_maintenance_date": "2023-05-01", "use_time": 720.5, "item_list": []}], "maintain_record_list": [], "check_record_list": [], "change_record_list": []}}}}}}, "security": [{"odooAuth": []}]}}}, "components": {"schemas": {"GetEquipmentInfoRequest": {"type": "object", "required": ["equipment_id"], "properties": {"equipment_id": {"type": "integer", "description": "设备ID"}}}, "GetEquipmentInfoResponse": {"type": "object", "properties": {"state": {"type": "string", "description": "请求状态", "enum": ["success", "error"]}, "msgs": {"type": "string", "description": "状态消息"}, "equipment": {"$ref": "#/components/schemas/EquipmentInfo"}}}, "EquipmentInfo": {"type": "object", "properties": {"id": {"type": "integer", "description": "设备ID"}, "name": {"type": "string", "description": "设备名称"}, "code": {"type": "string", "description": "设备编号"}, "category": {"type": "string", "description": "设备类别名称"}, "specification": {"type": "string", "description": "规格型号"}, "e_state": {"type": "string", "description": "设备状态", "enum": ["闲置", "在用", "报废", "报修"]}, "plant_name": {"type": "string", "description": "车间名称"}, "workshop_name": {"type": "string", "description": "产线名称"}, "work_center_name": {"type": "string", "description": "工位名称"}, "user_name": {"type": "string", "description": "设备负责人姓名"}, "manufacture_date": {"type": "string", "format": "date", "description": "生产日期，格式：YYYY-MM-DD"}, "warranty_date": {"type": "string", "format": "date", "description": "保修期截止日期，格式：YYYY-MM-DD"}, "repair_record_list": {"type": "array", "description": "维修记录列表（最近2条）", "items": {"$ref": "#/components/schemas/RepairRecord"}}, "maintain_record_list": {"type": "array", "description": "保养记录列表（最近2条）", "items": {"$ref": "#/components/schemas/MaintenanceRecord"}}, "check_record_list": {"type": "array", "description": "点检记录列表（最近2条）", "items": {"$ref": "#/components/schemas/CheckRecord"}}, "change_record_list": {"type": "array", "description": "更换件记录列表（最近2条）", "items": {"$ref": "#/components/schemas/ChangeRecord"}}}}, "RepairRecord": {"type": "object", "properties": {"id": {"type": "integer", "description": "维修记录ID"}, "report_user_id": {"type": "integer", "description": "报修人ID"}, "report_user_name": {"type": "string", "description": "报修人姓名"}, "repair_user_id": {"type": "integer", "description": "维修人ID"}, "repair_user_name": {"type": "string", "description": "维修人姓名"}, "report_time": {"type": "string", "format": "date", "description": "报修时间，格式：YYYY-MM-DD"}, "fault_description": {"type": "string", "description": "故障描述"}, "state": {"type": "string", "description": "维修状态"}, "maintenance_scheme": {"type": "string", "description": "维修方案名称"}, "finish_time": {"type": "string", "format": "date-time", "description": "完成时间，格式：YYYY-MM-DD HH:MM:SS"}, "equipment_name": {"type": "string", "description": "设备名称"}, "priority": {"type": "string", "description": "优先级"}, "last_maintenance_date": {"type": "string", "format": "date", "description": "上次维护日期，格式：YYYY-MM-DD"}, "use_time": {"type": "number", "description": "使用时间（小时）"}, "item_list": {"type": "array", "description": "维修项目列表", "items": {"type": "object"}}}}, "MaintenanceRecord": {"type": "object", "description": "保养记录，数据结构与维修记录相同，但类型为maintain"}, "CheckRecord": {"type": "object", "properties": {"id": {"type": "integer", "description": "点检记录ID"}, "code": {"type": "string", "description": "点检记录编号"}, "check_plan_id": {"type": "integer", "description": "点检方案ID"}, "check_plan_name": {"type": "string", "description": "点检方案名称"}, "assign_user_name": {"type": "string", "description": "指派人员姓名"}, "finish_time": {"type": "string", "format": "date", "description": "完成时间，格式：YYYY-MM-DD"}, "state": {"type": "string", "description": "点检状态"}, "description": {"type": "string", "description": "描述"}, "finish_user_id": {"type": "integer", "description": "完成人ID"}, "finish_user_name": {"type": "string", "description": "完成人姓名"}, "item_record_names": {"type": "string", "description": "点检项目名称（逗号分隔）"}, "item_record_list": {"type": "array", "description": "点检项目详细列表", "items": {"type": "object", "properties": {"id": {"type": "integer", "description": "点检项目记录ID"}, "check_item_id": {"type": "integer", "description": "点检项目ID"}, "check_item_name": {"type": "string", "description": "点检项目名称"}, "check_value": {"type": "string", "description": "检查值"}, "result": {"type": "string", "description": "检查结果"}, "description": {"type": "string", "description": "描述"}}}}}}, "ChangeRecord": {"type": "object", "properties": {"id": {"type": "integer", "description": "更换记录ID"}, "code": {"type": "string", "description": "更换记录编号"}, "name": {"type": "string", "description": "拆下部件名称"}, "new_name": {"type": "string", "description": "新部件名称"}, "record_date": {"type": "string", "format": "date", "description": "更换日期，格式：YYYY-MM-DD"}, "change_user_id": {"type": "integer", "description": "更换人ID"}, "change_user_name": {"type": "string", "description": "更换人姓名"}}}}, "securitySchemes": {"odooAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "Odoo用户认证"}}}}