<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--设备类别-->
    <!--search-->
    <record id="view_roke_mes_equipment_category_search" model="ir.ui.view">
        <field name="name">roke.mes.equipment.category.search</field>
        <field name="model">roke.mes.equipment.category</field>
        <field name="arch" type="xml">
            <search string="设备类别">
                <field name="name"/>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_mes_equipment_category_tree" model="ir.ui.view">
        <field name="name">roke.mes.equipment.category.tree</field>
        <field name="model">roke.mes.equipment.category</field>
        <field name="arch" type="xml">
            <tree string="设备类别">
                <field name="name"/>
                <field name="parent_id"/>
                <field name="note"/>
                <field name="company_id" groups="base.group_multi_company" optional="hide"/>
            </tree>
        </field>
    </record>

    <record id="view_roke_equipment_auxiliary_tree" model="ir.ui.view">
        <field name="name">roke.equipment.auxiliary.tree</field>
        <field name="model">roke.equipment.auxiliary</field>
        <field name="arch" type="xml">
            <tree string="附属设备信息" editable="bottom">
                <field name="auxiliary_id" invisible='1'/>
                <field name="equipment_name"/>
                <field name="specification"/>
                <field name="uom_id"/>
                <field name="acceptance_qty"/>
                <field name="note"/>
            </tree>
        </field>
    </record>

    <record id="view_roke_equipment_accessory_tree" model="ir.ui.view">
        <field name="name">roke.equipment.accessory.tree</field>
        <field name="model">roke.equipment.accessory</field>
        <field name="arch" type="xml">
            <tree string="配件信息" editable="bottom">
                <field name="accessory_id" invisible='1'/>
                <field name="accessory_name"/>
                <field name="specification"/>
                <field name="uom_id"/>
                <field name="acceptance_qty"/>
                <field name="note"/>
            </tree>
        </field>
    </record>

    <!--form-->
    <record id="view_roke_mes_equipment_category_form" model="ir.ui.view">
        <field name="name">roke.mes.equipment.category.form</field>
        <field name="model">roke.mes.equipment.category</field>
        <field name="arch" type="xml">
            <form>
                <group col="4">
                    <group>
                        <field name="name" string="类别名称"/>
                    </group>
                    <group>
                        <field name="parent_id" options="{'no_open': True}"/>
                    </group>
                    <group>
                        <field name="sequence"/>
                    </group>
                    <group>
                        <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}"/>
                    </group>
                </group>
                <group>
                    <field name="note"/>
                </group>
            </form>
        </field>
    </record>

    <record id="view_roke_equipment_auxiliary_form" model="ir.ui.view">
        <field name="name">roke.equipment.auxiliary.form</field>
        <field name="model">roke.equipment.auxiliary</field>
        <field name="arch" type="xml">
            <form string="附属设备信息">
                <group col="3">
                    <group>
                        <field name="auxiliary_id"/>
                        <field name="equipment_name"/>
                    </group>
                    <group>
                        <field name="specification"/>
                        <field name="uom_id"/>
                    </group>
                    <group>
                        <field name="acceptance_qty"/>
                        <field name="note"/>
                    </group>
                </group>
            </form>
        </field>
    </record>

    <record id="view_roke_equipment_accessory_form" model="ir.ui.view">
        <field name="name">roke.equipment.accessory.form</field>
        <field name="model">roke.equipment.accessory</field>
        <field name="arch" type="xml">
            <form string="配件信息">
                <group col="3">
                    <group>
                        <field name="accessory_id"/>
                        <field name="accessory_name"/>
                    </group>
                    <group>
                        <field name="specification"/>
                        <field name="uom_id"/>
                    </group>
                    <group>
                        <field name="acceptance_qty"/>
                        <field name="note"/>
                    </group>
                </group>
            </form>
        </field>
    </record>

    <!-- 录入方式-->
    <record id="view_roke_mes_input_type_tree" model="ir.ui.view">
        <field name="name">roke.mes.input.type.tree</field>
        <field name="model">roke.mes.input.type</field>
        <field name="arch" type="xml">
            <tree string="录入方式">
                <field name="name"/>
                <field name="index"/>
                <field name="input_type"/>
                <field name="company_id" groups="base.group_multi_company" optional="hide"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_mes_input_type_form" model="ir.ui.view">
        <field name="name">roke.mes.input.type.form</field>
        <field name="model">roke.mes.input.type</field>
        <field name="arch" type="xml">
            <form>
                <group col="4">
                    <group>
                        <field name="name"/>
                    </group>
                    <group>
                        <field name="index"/>
                    </group>
                    <group>
                        <field name="input_type"/>
                    </group>
                    <group>
                        <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}"/>
                    </group>
                </group>
            </form>
        </field>
    </record>
    <record id="view_roke_mes_input_type_action" model="ir.actions.act_window">
        <field name="name">录入方式</field>
        <field name="res_model">roke.mes.input.type</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="form_view_id" ref="view_roke_mes_input_type_form"/>
        <field name="domain">[]</field>
        <field name="context">{}</field>
    </record>

    <!--action-->
    <record id="view_roke_mes_equipment_category_action" model="ir.actions.act_window">
        <field name="name">设备类别</field>
        <field name="res_model">roke.mes.equipment.category</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="form_view_id" ref="view_roke_mes_equipment_category_form"/>
        <field name="domain">[]</field>
        <field name="context">{}</field>
    </record>

    <!--设备-->
    <!--search-->
    <record id="view_roke_mes_equipment_search" model="ir.ui.view">
        <field name="name">roke.mes.equipment.search</field>
        <field name="model">roke.mes.equipment</field>
        <field name="arch" type="xml">
            <search string="设备">
                <field string="设备" name="name"
                       filter_domain="['|', '|', ('name', 'ilike', self), ('code', 'ilike', self), ('specification', 'ilike', self)]"/>
                <field name="work_center_id"/>
                <field name="manufacturer"/>
                <field name="category_id"/>
                <group expand="1" string="Group By">
                    <filter string="工作中心" name="work_center_id" context="{'group_by':'work_center_id'}"/>
                    <filter string="生产厂家" name="manufacturer" context="{'group_by':'manufacturer'}"/>
                </group>
                <searchpanel>
                    <field name="category_id" icon="fa-wrench" enable_counters="1" expand="1"/>
                </searchpanel>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_mes_equipment_tree" model="ir.ui.view">
        <field name="name">roke.mes.equipment.tree</field>
        <field name="model">roke.mes.equipment</field>
        <field name="arch" type="xml">
            <tree string="设备">
                <field name="code"/>
                <field name="name"/>
                <field name="category_id"/>
                <field name="specification"/>
                <field name="manufacture_date" string="出厂日期"/>
                <field name="active_date"/>
                <field name="location"/>
                <field name="spot_plan_ids" optional="hide" widget="many2many_tags"/>
                <field name="parent_eqpt_id" optional="hide"/>
                <field name="last_spot_date" optional="hide"/>
                <field name="location" optional="hide"/>
                <field name="index_code" optional="hide"/>
                <field name="manufacturer" optional="hide"/>
                <field name="manufacturer" optional="hide"/>
                <field name="manufacturer_code" optional="hide"/>
                <field name="warranty_date" optional="hide"/>
                <field name="register_code" optional="hide"/>
                <field name="create_archives_user_id" optional="hide"/>
                <field name="archives_code" optional="hide"/>
                <field name="archives_date" optional="hide"/>
                <field name="use_permit_code" optional="hide"/>
                <field name="test_org_id" optional="hide"/>
                <field name="last_test_date" optional="hide"/>
                <field name="next_test_date" optional="hide"/>
                <field name="e_state" optional="show"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_mes_equipment_form" model="ir.ui.view">
        <field name="name">roke.mes.equipment.form</field>
        <field name="model">roke.mes.equipment</field>
        <field name="arch" type="xml">
            <form string="设备">
                <header>
                    <!-- <button name="repair_request" type="object" string="报修" class="oe_highlight"
                            attrs="{'invisible':[('in_repair', '=', True)]}"/>
                    <button name="btn_cr_qr_image" type="object" string="生成二维码" class="oe_highlight"/> -->
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box"/>
                    <widget name="web_ribbon" text="正常" bg_color="bg-success"
                            attrs="{'invisible': ['|', ('active', '=', False), ('in_repair', '=', True)]}"/>
                    <widget name="web_ribbon" text="归档" bg_color="bg-danger"
                            attrs="{'invisible': [('active', '=', True)]}"/>
                    <widget name="web_ribbon" text="报修" bg_color="bg-warning"
                            attrs="{'invisible': ['|', ('active', '=', False), ('in_repair', '=', False)]}"/>
                    <field name="active" invisible="1"/>
                    <field name="in_repair" invisible="1"/>
                    <group string="设备基础信息" col="3">
                        <group>
                            <field name="name" required="1"/>
                            <field name="specification" required="1"/>
                            <field name="e_state" required="1"/>
                        </group>
                        <group>
                            <field name="code"/>
                            <field name="manufacture_date"/>
                        </group>
                        <group>
                            <field name="category_id" required="1" options="{'no_open': True}"/>
                            <field name="active_date" required="1"/>
                        </group>
                    </group>
                    <group string="其他信息" col="3">
                        <group>
                            <field name="manufacturer"/>
                        </group>
                        <group>
                            <field name="warranty_date"/>
                        </group>
                    </group>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>

    <!--    <record id="view_roke_mes_equipment_pivot" model="ir.ui.view">-->
    <!--        <field name="name">roke.mes.equipment.pivot</field>-->
    <!--        <field name="model">roke.mes.equipment</field>-->
    <!--        <field name="arch" type="xml">-->
    <!--            <pivot display_quantity="True">-->
    <!--                <field name="create_date"  type="row"/>-->
    <!--                <field name="code" type="row"/>-->
    <!--            </pivot>-->
    <!--        </field>-->
    <!--    </record>-->
    <!--pivot-->
    <record model="ir.ui.view" id="view_roke_mes_equipment_pivot">
        <field name="name">roke.mes.equipment.pivot</field>
        <field name="model">roke.mes.equipment</field>
        <field name="arch" type="xml">
            <pivot string="设备" display_quantity="True" sample="1">
                <field name="name" type="col"/>
                <field name="category_id" type="row"/>
            </pivot>
        </field>
    </record>


    <!--action-->
    <record id="view_roke_mes_equipment_action" model="ir.actions.act_window">
        <field name="name">设备</field>
        <field name="res_model">roke.mes.equipment</field>
        <field name="view_mode">tree,form,pivot</field>
        <field name="type">ir.actions.act_window</field>
        <field name="form_view_id" ref="view_roke_mes_equipment_form"/>
        <field name="domain">[]</field>
        <field name="context">{}</field>
    </record>

</odoo>
