odoo.define('roke_mes_base.attachment_preview', function (require) {
    'use strict';


    const components = {
        Attachment: require("mail/static/src/components/attachment/attachment.js"),
    };
    const { patch } = require('web.utils');
    const session = require('web.session');
    const { CommonUtils } = require('roke_mes_base.Utils');
    const Dialog = require('web.Dialog');


    patch(
        components.Attachment,
        'roke_mes_base.attachment_preview',
        {

            _openPreviewInDialog: function (preview_url) {
                let dialog = new Dialog(this, {
                    title: '文档预览', size: 'large', renderFooter: false,
                    $content: $('<div>', {
                        html: `  
                            <iframe src="${preview_url}" width="100%" height="600px" scrolling="yes" frameborder="0"></iframe>                    
                        `,
                    }),
                });
                dialog.open();
            },

            /**
             * Open the attachment viewer when clicking on viewable attachment.
             *
             * @private
             * @param {MouseEvent} ev
             */

            async _onClickImage(ev) {
                let self = this;
                let attachment_id = this.attachment.id;
                let url;
                let web_base_url = session['web.base.url'];
                let attachment = await CommonUtils._previewAttachmentURL(attachment_id);
                const { code, message, data } = attachment;
                // TODO 文档 Chatter 中的附件上传需要改造，以支持工业图纸上传
                if (code === 1) {
                    this.env.services.notification.notify({
                        title: "无法预览此文档", message: message, type: "warning",
                    });
                    return;
                } else {
                    url = `${web_base_url}${data.url}`;
                }
                let result = await CommonUtils._makePreviewURL(url);
                if (result.code === 1) {
                    this.env.services.notification.notify({
                        title: result.data.title, message: result.data.content, type: "warning",
                    });
                    return;
                } else {
                    let preview_url = result.data.url
                    self._openPreviewInDialog(preview_url);
                }
            },
        });

});