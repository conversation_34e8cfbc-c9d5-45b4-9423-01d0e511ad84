<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--成品仓库-->
    <!--search-->
    <record id="view_roke_finished_warehouse_search" model="ir.ui.view">
        <field name="name">roke.finished.warehouse.search</field>
        <field name="model">roke.finished.warehouse</field>
        <field name="arch" type="xml">
            <search string="成品仓库">
                <field name="name"/>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_finished_warehouse_tree" model="ir.ui.view">
        <field name="name">roke.finished.warehouse.tree</field>
        <field name="model">roke.finished.warehouse</field>
        <field name="arch" type="xml">
            <tree string="成品仓库">
                <field name="name"/>
                <field name="create_uid" string="创建人"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_finished_warehouse_form" model="ir.ui.view">
        <field name="name">roke.finished.warehouse.form</field>
        <field name="model">roke.finished.warehouse</field>
        <field name="arch" type="xml">
            <form string="成品仓库">
                <sheet>
                    <group id="g1">
                        <group>
                            <group>
                                <field name="name"/>
                            </group>
                            <group>
                                <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}"/>
                            </group>
                        </group>
                        <group>
                            <group></group>
                            <group></group>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>
    <!--action-->
    <record id="view_roke_finished_warehouse_action" model="ir.actions.act_window">
        <field name="name">成品仓库</field>
        <field name="res_model">roke.finished.warehouse</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{}</field>
        <field name="form_view_id" ref="view_roke_finished_warehouse_form"/>
        <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
            点击左上角“创建”按钮新增一个成品仓库。
          </p>
        </field>
    </record>

</odoo>
