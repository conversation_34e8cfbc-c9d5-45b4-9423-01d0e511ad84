# API Documentation for `/roke/spare_part/list`

## 基本信息
- **接口名称**: 获取备件数据列表
- **接口描述**: 获取备件数据列表，支持按备件名称模糊搜索，支持分页
- **请求方式**: POST
- **认证方式**: 用户认证 (auth="user")
- **数据格式**: JSON
- **CORS支持**: 是

## 请求参数

### 请求头
```
Content-Type: application/json
Authorization: Bearer <token>
```

### 请求体参数
| 参数名 | 类型 | 必填 | 默认值 | 描述 |
|-------|------|-----|-------|------|
| name | String | 否 | - | 备件名称，支持模糊搜索 |
| page | Integer | 否 | 1 | 页码，从1开始 |
| page_size | Integer | 否 | 10 | 每页数量，最大100 |

### 请求示例

#### 获取所有备件（第一页）
```json
{
  "page": 1,
  "page_size": 10
}
```

#### 按名称搜索备件
```json
{
  "name": "轴承",
  "page": 1,
  "page_size": 10
}
```

#### 获取第二页数据
```json
{
  "name": "轴承",
  "page": 2,
  "page_size": 20
}
```

## 响应数据

### 成功响应
```json
{
  "state": "success",
  "msgs": "获取成功",
  "data": {
    "spare_parts": [
      {
        "id": 1,
        "name": "深沟球轴承",
        "code": "SP000001",
        "model": "6205-2RS",
        "manufacturer": "SKF",
        "theoretical_life": 24,
        "life_unit": "month",
        "theoretical_life_display": "24月",
        "usage_count": 5,
        "image_url": "/web/image/roke.spare.part/1/image",
        "uom_name": "个",
        "note": "高质量轴承，适用于高速运转设备"
      },
      {
        "id": 2,
        "name": "圆锥滚子轴承",
        "code": "SP000002",
        "model": "30205",
        "manufacturer": "FAG",
        "theoretical_life": 18,
        "life_unit": "month",
        "theoretical_life_display": "18月",
        "usage_count": 3,
        "image_url": "/web/image/roke.spare.part/2/image",
        "uom_name": "个",
        "note": "适用于重载设备"
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 10,
      "total_count": 25,
      "total_pages": 3,
      "has_next": true,
      "has_prev": false
    }
  }
}
```

### 错误响应
```json
{
  "state": "error",
  "msgs": "获取备件列表失败: 具体错误信息"
}
```

## 响应字段详细说明

### 备件信息 (spare_parts)
| 字段名 | 类型 | 描述 |
|-------|------|------|
| id | Integer | 备件ID |
| name | String | 备件名称 |
| code | String | 备件编号（自动生成） |
| model | String | 型号 |
| manufacturer | String | 厂家 |
| theoretical_life | Integer | 理论寿命数值 |
| life_unit | String | 寿命单位（year/month） |
| theoretical_life_display | String | 理论寿命显示文本（如：24月） |
| usage_count | Integer | 已使用数量（基于使用记录计算） |
| image_url | String | 图片访问URL |
| uom_name | String | 单位名称 |
| note | String | 备注信息 |

### 分页信息 (pagination)
| 字段名 | 类型 | 描述 |
|-------|------|------|
| page | Integer | 当前页码 |
| page_size | Integer | 每页数量 |
| total_count | Integer | 总记录数 |
| total_pages | Integer | 总页数 |
| has_next | Boolean | 是否有下一页 |
| has_prev | Boolean | 是否有上一页 |

## 实现细节

### 查询逻辑
1. **参数解析**: 获取name、page、page_size参数
2. **条件构建**: 如果提供name参数，使用ilike进行模糊搜索
3. **分页计算**: 计算offset和limit
4. **数据查询**: 按创建时间倒序查询备件数据
5. **数据组装**: 组装返回数据，包括图片URL处理

### 图片处理
```python
# 生成图片访问URL
if spare_part.image:
    image_url = f"/web/image/roke.spare.part/{spare_part.id}/image"
```

### 理论寿命显示
```python
# 格式化理论寿命显示
if spare_part.theoretical_life:
    unit_name = "年" if spare_part.life_unit == "year" else "月"
    theoretical_life_display = f"{spare_part.theoretical_life}{unit_name}"
```

### 已使用数量计算
已使用数量通过计算字段`usage_record_count`获取，该字段统计关联的使用记录数量。

## 使用示例

### JavaScript调用示例
```javascript
async function getSparePartList(filters = {}) {
  try {
    const requestData = {
      page: filters.page || 1,
      page_size: filters.page_size || 10
    };
    
    // 添加搜索条件
    if (filters.name) {
      requestData.name = filters.name;
    }
    
    const response = await fetch('/roke/spare_part/list', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + token
      },
      body: JSON.stringify(requestData)
    });
    
    const data = await response.json();
    
    if (data.state === 'success') {
      console.log('备件列表:', data.data.spare_parts);
      console.log('分页信息:', data.data.pagination);
      return data.data;
    } else {
      console.error('获取失败:', data.msgs);
      return null;
    }
  } catch (error) {
    console.error('请求错误:', error);
    return null;
  }
}

// 使用示例
// 获取第一页所有备件
const allParts = await getSparePartList();

// 搜索轴承相关备件
const bearingParts = await getSparePartList({ name: '轴承' });

// 获取第二页数据
const secondPage = await getSparePartList({ page: 2, page_size: 20 });
```

### Vue.js组件示例
```vue
<template>
  <div class="spare-part-list">
    <!-- 搜索框 -->
    <div class="search-box">
      <input 
        v-model="searchName" 
        @input="onSearch"
        placeholder="请输入备件名称搜索"
        class="search-input"
      />
    </div>
    
    <!-- 备件列表 -->
    <div class="part-grid">
      <div 
        v-for="part in sparePartList" 
        :key="part.id"
        class="part-card"
      >
        <img 
          v-if="part.image_url" 
          :src="part.image_url" 
          :alt="part.name"
          class="part-image"
        />
        <div class="part-info">
          <h3>{{ part.name }}</h3>
          <p>型号: {{ part.model }}</p>
          <p>厂家: {{ part.manufacturer }}</p>
          <p>理论寿命: {{ part.theoretical_life_display }}</p>
          <p>已使用: {{ part.usage_count }}次</p>
        </div>
      </div>
    </div>
    
    <!-- 分页组件 -->
    <div class="pagination">
      <button 
        @click="prevPage" 
        :disabled="!pagination.has_prev"
      >
        上一页
      </button>
      <span>{{ pagination.page }} / {{ pagination.total_pages }}</span>
      <button 
        @click="nextPage" 
        :disabled="!pagination.has_next"
      >
        下一页
      </button>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      sparePartList: [],
      pagination: {},
      searchName: '',
      currentPage: 1,
      pageSize: 10
    };
  },
  
  mounted() {
    this.loadSparePartList();
  },
  
  methods: {
    async loadSparePartList() {
      const data = await getSparePartList({
        name: this.searchName,
        page: this.currentPage,
        page_size: this.pageSize
      });
      
      if (data) {
        this.sparePartList = data.spare_parts;
        this.pagination = data.pagination;
      }
    },
    
    onSearch() {
      this.currentPage = 1;
      this.loadSparePartList();
    },
    
    prevPage() {
      if (this.pagination.has_prev) {
        this.currentPage--;
        this.loadSparePartList();
      }
    },
    
    nextPage() {
      if (this.pagination.has_next) {
        this.currentPage++;
        this.loadSparePartList();
      }
    }
  }
};
</script>
```

### cURL调用示例
```bash
# 获取所有备件
curl -X POST "http://localhost:8069/roke/spare_part/list" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{"page": 1, "page_size": 10}'

# 搜索轴承
curl -X POST "http://localhost:8069/roke/spare_part/list" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{"name": "轴承", "page": 1, "page_size": 10}'
```

## 应用场景

1. **备件管理系统**: 展示所有备件信息
2. **备件选择**: 在维修或保养时选择备件
3. **库存管理**: 查看备件使用情况
4. **移动端应用**: 在移动设备上浏览备件
5. **备件搜索**: 快速查找特定备件

## 性能考虑

1. **分页查询**: 使用limit和offset进行分页，避免一次加载过多数据
2. **索引优化**: 建议在name字段上建立索引以提高搜索性能
3. **图片处理**: 图片URL按需生成，不预加载图片数据
4. **缓存策略**: 可以考虑对不经常变化的备件数据进行缓存

## 扩展建议

1. **高级搜索**: 添加按厂家、型号、类别等字段的搜索
2. **排序功能**: 添加按名称、使用次数、创建时间等排序
3. **筛选功能**: 添加按寿命单位、使用状态等筛选
4. **批量操作**: 支持批量选择备件
5. **导出功能**: 支持导出备件列表到Excel

## 错误处理

### 常见错误
1. **参数错误**: page或page_size为非正整数
2. **权限错误**: 用户没有访问备件的权限
3. **系统错误**: 数据库连接失败等

### 错误响应格式
```json
{
  "state": "error",
  "msgs": "具体错误描述"
}
```

## 快速参考

### 请求格式
```bash
POST /roke/spare_part/list
Content-Type: application/json
Authorization: Bearer <token>

{
  "name": "轴承",        // 可选：备件名称模糊搜索
  "page": 1,            // 可选：页码，默认1
  "page_size": 10       // 可选：每页数量，默认10
}
```

### 响应格式
```json
{
  "state": "success",
  "msgs": "获取成功",
  "data": {
    "spare_parts": [
      {
        "id": 1,
        "name": "备件名称",
        "code": "备件编号",
        "model": "型号",
        "manufacturer": "厂家",
        "theoretical_life": 24,
        "life_unit": "month",
        "theoretical_life_display": "24月",
        "usage_count": 5,
        "image_url": "/web/image/roke.spare.part/1/image",
        "uom_name": "个",
        "note": "备注"
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 10,
      "total_count": 25,
      "total_pages": 3,
      "has_next": true,
      "has_prev": false
    }
  }
}
```

### 关键特性
- ✅ 支持备件名称模糊搜索
- ✅ 完整的分页功能
- ✅ 返回备件完整信息
- ✅ 自动计算已使用数量
- ✅ 图片URL自动生成
- ✅ 理论寿命格式化显示
- ✅ 支持CORS跨域访问
- ✅ 需要用户认证

## 导入到Apifox

我已经创建了一个名为 `roke_spare_part_list_api_doc.json` 的文件，其中包含了完整的OpenAPI规范文档。您可以将此文件导入到Apifox中：

1. 打开Apifox
2. 点击"导入"按钮
3. 选择"OpenAPI"格式
4. 上传或粘贴`roke_spare_part_list_api_doc.json`文件内容
5. 完成导入

这样您就可以在Apifox中查看和测试这个API了。
