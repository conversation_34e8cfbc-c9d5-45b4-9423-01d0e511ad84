odoo.define('roke_mes_base.ITS_TIME_TO_RENEW_THE_FEE', async function (require) {
    'use strict';

    const rpc = require("web.rpc");
    const session = require("web.session");
    const localStorage = require('web.local_storage');

    const ActionManager = require('web.ActionManager');


    async function handleDeadline() {
        let currentTime = new Date().getTime();
        let rddeadline = localStorage.getItem('so.rqddline');
        let fcdeadline = localStorage.getItem('so.fcddline');
        if (!(rddeadline && fcdeadline && currentTime < parseInt(rddeadline))) {
            const { deadline, series } = await getFactoryDeadline();
            setFactoryExp(deadline, series);
            setRequestExp();
        }
    }

    function setRequestExp() {
        let currentTime = new Date().getTime();
        // 1天 = 24小时 * 60分钟 * 60秒 * 1000毫秒
        let rqdeadline = currentTime + (24 * 60 * 60 * 1000);
        // let rqdeadline = currentTime + (20 * 1000);
        localStorage.setItem('so.rqddline', rqdeadline);
    }

    function setFactoryExp(dateString, series) {
        localStorage.setItem('service.series', series);
        if (dateString == "*") {
            localStorage.setItem('so.fcddline', dateString);
        } else {
            let convertDate = new Date(dateString).getTime();
            localStorage.setItem('so.fcddline', convertDate);
        }
    }

    function calcuteWithin30Days() {
        let fcdeadline = localStorage.getItem('so.fcddline');
        if (fcdeadline == "*") {
            return { isWithin30Days: false, dayDifference: -1 };
        } else {
            let convertDate = parseInt(fcdeadline);
            let currentDate = new Date();
            let timeDifference = convertDate - currentDate.getTime();
            let dayDifference = timeDifference / (1000 * 60 * 60 * 24);
            let isWithin30Days = dayDifference < 30;
            return { isWithin30Days: isWithin30Days, dayDifference: dayDifference };
        }
    }

    function getFactoryDeadline() {
        return rpc.query({
            model: 'res.config.settings',
            method: 'message_fetch_poll',
        }).then(result => {
            return result;
        });
    }

    async function showTips() {
        await handleDeadline();
        const { isWithin30Days, dayDifference } = calcuteWithin30Days();
        if (isWithin30Days) {
            if ($(".o_action_manager")) {
                $(".fcdeadline_tip").remove();
                if (dayDifference >= 0){
                    let serise = localStorage.getItem('service.series')
                    let tip = `
                        <div name="message" class="alert alert-info fcdeadline_tip" style="padding: 0.9rem 1.25rem;" role="alert">
                            <i class="fa fa-exclamation-circle"/>
                            <span style="margin-left: 8px;">
                                您的${serise ? '<strong>' + serise + '</strong>' : ''}服务剩余<strong>${parseInt(dayDifference)}</strong>天，请及时联系客服400-006-0611续费。
                            </span>
                        </div>
                    `
                    $(".o_action_manager").prepend(tip);
                }else{
                    session.session_logout();
                    location.reload();
                }
            }
        } 
    }

    $("*").on("click", async function (ev) {
        await showTips();
    });


    ActionManager.include({
        on_attach_callback: function () {
            this._super.apply(this, arguments);
            showTips();
        }
    });

});
