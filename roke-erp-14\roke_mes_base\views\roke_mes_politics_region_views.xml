<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--search-->
    <record id="view_roke_mes_politics_region_search" model="ir.ui.view">
        <field name="name">roke.mes.politics.region.search</field>
        <field name="model">roke.mes.politics.region</field>
        <field name="arch" type="xml">
            <search string="行政区域">
                <field name="adcode"/>
                <field name="name"/>
            </search>
        </field>
    </record>

    <!--tree-->
    <record id="view_roke_mes_politics_region_tree" model="ir.ui.view">
        <field name="name">roke.mes.politics.region.tree</field>
        <field name="model">roke.mes.politics.region</field>
        <field name="arch" type="xml">
            <tree string="行政区域" create="0" delete="0" edit="0" duplicate="0">
                <field name="adcode"/>
                <field name="name"/>
                <field name="complete_name"/>
                <field name="level"/>
                <field name="center"/>
                <field name="parent_id"/>
                <field name="create_uid" string="创建人"/>
                <field name="create_date" string="创建时间"/>
            </tree>
        </field>
    </record>

    <!--form-->
    <record id="view_roke_mes_politics_region_form" model="ir.ui.view">
        <field name="name">roke.mes.politics.region.form</field>
        <field name="model">roke.mes.politics.region</field>
        <field name="arch" type="xml">
            <form string="行政区域" create="0" delete="0" edit="0" duplicate="0">
                <header>
                    <button name="auto_update_area" string="获取最新行政区域信息" type="object" class="oe_highlight"/>
                </header>
                <group id="g1">
                    <group>
                        <group>
                            <field name="adcode" readonly="1"/>
                            <field name="parent_id"/>
                        </group>
                        <group>
                            <field name="name" required="1"/>
                        </group>
                    </group>
                    <group>
                        <group>
                            <field name="center" required="1"/>
                        </group>
                        <group>
                            <field name="level"/>
                        </group>
                    </group>
                </group>
                <notebook>
                    <page string='下级行政区域'>
                        <field name="child_ids">
                            <tree>
                                <field name="adcode"/>
                                <field name="name"/>
                                <field name="center"/>
                                <field name="parent_id"/>
                            </tree>
                        </field>
                    </page>
                </notebook>
            </form>
        </field>
    </record>

    <!--action-->
    <record id="view_roke_mes_politics_region_action" model="ir.actions.act_window">
        <field name="name">行政区域</field>
        <field name="res_model">roke.mes.politics.region</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{}</field>
        <field name="form_view_id" ref="view_roke_mes_politics_region_form"/>
    </record>

</odoo>
