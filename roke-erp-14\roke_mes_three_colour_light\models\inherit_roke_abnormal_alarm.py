from odoo import api, models, fields


class InheritRokeAbnormalAlarmRecord(models.Model):
    _inherit = "roke.abnormal.alarm"

    light_id = fields.Many2one("roke.stack.light", string="安灯")

    @api.model
    def create(self, vals):
        res = super(InheritRokeAbnormalAlarmRecord, self).create(vals)
        config_id = self.env["roke.stack.light.config"].search([("name", "=", res.abnormal_id.id)], limit=1)
        if config_id.is_notify == "是":
            if config_id.mini_program:
                if config_id.notify_user_ids:
                    users = config_id.notify_user_ids
                else:
                    users = config_id.notify_group_id.users
                val_list = []
                for item in users:
                    message_data = {
                        "thing4": {
                            "value": item.name
                        },
                        "thing3": {
                            "value": res.abnormal_id.name
                        },
                        "thing2": {
                            "value": f"{res.plant_id.name}-{res.workshop_id.name}-{res.work_center.name}"
                        },
                    }
                    val_list.append({
                        "user_id": item.id,
                        "message_data": message_data
                    })
                wechat_notify_ids = self.env["wechat.notify"].sudo().create(val_list)
                wechat_notify_ids.send_wechat_notification(res.id)
            if config_id.email:
                if config_id.notify_user_ids:
                    users = config_id.notify_user_ids
                else:
                    users = config_id.notify_group_id.users
                for item in users:
                    res.activity_schedule(
                        'mail.mail_activity_data_todo',
                        user_id=item.id,
                        note=f"异常类型：{res.abnormal_id.name}，设备：{res.equipment_id.name}"
                    )
                    
        return res