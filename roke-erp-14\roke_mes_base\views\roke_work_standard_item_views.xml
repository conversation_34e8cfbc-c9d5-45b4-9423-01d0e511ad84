<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--作业规范/标准-->
    <!--tree-->
    <record id="view_roke_work_standard_item_tree" model="ir.ui.view">
        <field name="name">roke.work.standard.item.tree</field>
        <field name="model">roke.work.standard.item</field>
        <field name="arch" type="xml">
            <tree string="作业规范/标准">
                <field name="sequence" widget="handle"/>
                <field name="title"/>
                <field name="name"/>
                <field name="process_id" invisible="1"/>
                <field name="routing_line_id" invisible="1"/>
                <field name="create_uid" string="创建人"/>
            </tree>
        </field>
    </record>
    <record id="view_roke_work_standard_item_editable_tree" model="ir.ui.view">
        <field name="name">roke.work.standard.item.tree</field>
        <field name="model">roke.work.standard.item</field>
        <field name="priority">1000</field>
        <field name="arch" type="xml">
            <tree string="作业规范/标准">
                <field name="sequence" widget="handle"/>
                <field name="title" required="1"/>
                <field name="name" required="1"/>
                <field name="description"/>
                <field name="process_id" invisible="1"/>
                <field name="routing_line_id" invisible="1"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_work_standard_item_form" model="ir.ui.view">
        <field name="name">roke.work.standard.item.form</field>
        <field name="model">roke.work.standard.item</field>
        <field name="arch" type="xml">
            <form string="作业规范/标准">
                <sheet>
                    <group id="g1">
                        <group>
                            <field name="title" required="1"/>
                            <field name="process_id" invisible="1"/>
                            <field name="routing_line_id" invisible="1"/>
                            <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}"/>
                        </group>
                        <group>
                            <field name="name" required="1"/>
                            <field name="image_1920" widget='image' options='{"zoom": true, "preview_image":"image_128"}'/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

</odoo>
