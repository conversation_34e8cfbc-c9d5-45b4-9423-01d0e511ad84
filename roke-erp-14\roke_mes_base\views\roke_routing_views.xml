<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--工艺路线-->
    <!--search-->
    <record id="view_roke_routing_search" model="ir.ui.view">
        <field name="name">roke.routing.search</field>
        <field name="model">roke.routing</field>
        <field name="arch" type="xml">
            <search string="工艺路线">
                <field name="name"/>
                <field name="code"/>
                <field name="internal_code"/>
                <filter string="已归档" name="inactive" domain="[('active', '=', False)]"/>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_routing_tree" model="ir.ui.view">
        <field name="name">roke.routing.tree</field>
        <field name="model">roke.routing</field>
        <field name="arch" type="xml">
            <tree string="工艺路线">
                <header>
                    <button name="make_confirm" string="批量确认" type="object" class="oe_highlight fa fa-check"/>
                    <button name="cancel_confirm" string="批量取消确认" type="object" class="fa fa-close"/>
                </header>
                <field name="internal_code" optional="hide"/>
                <field name="code"/>
                <field name="name"/>
                <field name="note" optional="show"/>
                <field name="state"/>
                <field name="process_description"/>
                <field name="create_uid" string="创建人" optional="show"/>
                <field name="create_date" string="创建时间" optional="show"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_routing_form" model="ir.ui.view">
        <field name="name">roke.routing.form</field>
        <field name="model">roke.routing</field>
        <field name="arch" type="xml">
            <form string="工艺路线">
                <header>
                    <button name="reference_create_routing_line_action" string="明细参照已有工艺" type="object"
                            class="oe_highlight oe_read_only" attrs="{'invisible': [('active', '=', False)]}"/>
                    <button name="make_confirm" string="确认" type="object" class="oe_highlight"
                            attrs="{'invisible': [('state', '=', '确认')]}"/>
                    <button name="cancel_confirm" string="取消确认" type="object"
                            attrs="{'invisible': [('state', '=', '待确认')]}"/>
                </header>
                <div class="oe_button_box" name="button_box"/>
                <widget name="web_ribbon" text="归档" bg_color="bg-danger" attrs="{'invisible': [('active', '=', True)]}"/>
                <group id="g1" col="4">
                    <group>
                        <field name="name"/>
                        <field name="active" invisible="1"/>
<!--                        <label for="template_id"/>-->
<!--                        <div name="template_id" class="o_row">-->
<!--                            <field name="template_id" options="{'no_create': True}"/>-->
<!--                            <span>-->
<!--                                <button name='line_reference' string='参照明细' type='object' class='oe_link' attrs="{'invisible': [('template_id', '=', False)]}"/>-->
<!--                            </span>-->
<!--                        </div>-->
                    </group>
                    <group>
                        <field name="code"/>
                        <!--单件批次号-->
                    </group>
                    <group>
                        <field name="state"/>
                    </group>
                    <group>
                        <field name="internal_code"/>
                        <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}"/>
                    </group>
                </group>
                <notebook>
                    <page string="工艺明细" name="line_ids">
                        <button name="multi_add_routing_action" string="批量添加工序" type="object" class="oe_highlight"
                                attrs="{'invisible': [('active', '=', False)]}"/>
                        <field name="line_ids" context="{'tree_view_ref': 'roke_mes_base.view_roke_routing_line_editable_tree'}"/>
                    </page>
                    <page string="可用产品">
                        <field name="product_ids" options="{'no_create': True}"/>
                    </page>
                </notebook>
                <group id="g2">
                    <field name="note" nolabel="1" placeholder="此处可以填写备注或描述" />
                </group>
                <div class="oe_chatter">
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>
    <!--action-->
    <record id="view_roke_routing_action" model="ir.actions.act_window">
        <field name="name">工艺路线</field>
        <field name="res_model">roke.routing</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="view_ids" eval="[(5, 0, 0),
            (0, 0, {'view_mode': 'tree', 'view_id': ref('roke_mes_base.view_roke_routing_tree')}),
            (0, 0, {'view_mode': 'form', 'view_id': ref('roke_mes_base.view_roke_routing_form')})]"/>
        <field name="domain">[]</field>
        <field name="context">{}</field>
        <field name="form_view_id" ref="view_roke_routing_form"/>
        <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
            点击左上角“创建”按钮新增一个工序。
          </p><p>
            或者您也可以选择批量导入功能一次性导入多个工序。
          </p>
        </field>
    </record>

    <!--工艺明细-->
    <!--tree-->
    <record id="view_roke_routing_line_editable_tree" model="ir.ui.view">
        <field name="name">roke.routing.line.editable.tree</field>
        <field name="model">roke.routing.line</field>
        <field name="priority">1000</field>
        <field name="arch" type="xml">
            <tree string="工艺明细-可编辑" editable="bottom" create="0">
                <field name="multiple" invisible="1"/>
                <field name="routing_id" invisible="1"/>
                <field name="sequence"/>
                <field name="process_id"/>
                <!--可忽略-->
                <!--完工数合并-->
                <!--计薪规则-->
                <!--结算方式-->
                <field name="work_qty"/>
                <field name="finished_qty"/>
                <!--消耗工序，物料管理-->
                <field name="child_process_ids" invisible="1"/>
                <field name="rated_working_hours" attrs="{'readonly': [('child_process_ids', '!=', [])]}"/>
                <field name="prepare_work_hours"/>
                <!--质检方式-->
                <!--质检相关内容-->
                <field name="standard_items_number" string="作业规范"/>
                <button name="action_standard_items_view" string="作业规范" title="作业规范" type="object" icon="fa-list-alt"/>
                <field name="child_process_number" string="子工序"/>
                <button name="action_child_process_view" string="子工序" title="子工序" type="object" icon="fa-arrow-circle-down"/>
                <field name="note" optional="hide"/>
                <field name="create_uid" string="创建人" optional="hide"/>
            </tree>
        </field>
    </record>
    <record id="view_roke_routing_line_tree" model="ir.ui.view">
        <field name="name">roke.routing.line.tree</field>
        <field name="model">roke.routing.line</field>
        <field name="arch" type="xml">
            <tree string="工艺明细">
                <field name="routing_id"/>
                <field name="sequence"/>
                <field name="process_id"/>
                <field name="work_qty"/>
                <field name="finished_qty"/>
                <field name="direct_labor_price"/>
                <field name="multiple" invisible="1"/>
                <field name="note" optional="show"/>
                <field name="create_uid" string="创建人" optional="show"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_routing_line_form" model="ir.ui.view">
        <field name="name">roke.routing.line.form</field>
        <field name="model">roke.routing.line</field>
        <field name="arch" type="xml">
            <form string="工艺明细">
                <sheet>
                    <group id="g1" col="3">
                        <group>
                            <field name="sequence"/>
                            <field name="process_id"/>
                            <field name="multiple" invisible="1"/>
                        </group>
                        <group>
                            <field name="work_qty"/>
                            <field name="finished_qty"/>
                        </group>
                        <group>
                            <field name="routing_id"/>
                            <field name="direct_labor_price"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="作业规范" name="standard_item">
                            <field name="standard_item_ids">
                                <tree editable="bottom">
                                    <field name="sequence" widget="handle"/>
                                    <field name="title"/>
                                    <field name="name"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                    <group id="g2">
                        <field name="note" nolabel="1" placeholder="此处可以填写备注或描述" />
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="view_roke_routing_document_form" model="ir.ui.view">
        <field name="name">roke.routing.document.form</field>
        <field name="model">roke.routing</field>
        <field name="priority">1000</field>
        <field name="arch" type="xml">
            <form string="工艺路线" js_class="roke_routing_form" create="0" duplicate="0" form_import="0">
                <header>
                        <button name="reference_create_routing_line_action" string="明细参照已有工艺" type="object"
                                class="oe_highlight oe_read_only" attrs="{'invisible': [('active', '=', False)]}"/>
                </header>
                <div class="oe_button_box" name="button_box"/>
                <widget name="web_ribbon" text="归档" bg_color="bg-danger" attrs="{'invisible': [('active', '=', True)]}"/>
                <div style="display: flex;">
                    <div class="preview_container only_process_planning" style="width: 45%;">
                        <xcpreviewer/>
                    </div>
                    <div style="width: 55%; padding-left: 20px;">
                        <group id="g1">
                            <group id="g2">
                                <field name="name"/>
                            </group>
                            <group>
                                <field name="active"/>
                            </group>
                        </group>
                        <notebook/>
                        <group id="g2">
                            <field name="note" nolabel="1" placeholder="此处可以填写备注或描述" />
                        </group>
                        <div class="oe_chatter">
                            <field name="message_ids" widget="mail_thread"/>
                        </div>
                    </div>
                </div>
            </form>
        </field>
    </record>

</odoo>
