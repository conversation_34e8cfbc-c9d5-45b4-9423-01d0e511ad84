{"openapi": "3.0.1", "info": {"title": "ROKE Equipment API", "description": "API documentation for ROKE Equipment module", "version": "1.0.0"}, "servers": [{"url": "http://localhost:8069", "description": "Local development server"}], "paths": {"/roke/get/equipment/maintenance_info": {"post": {"tags": ["Equipment"], "summary": "获取设备维护信息", "description": "获取设备点检、保养、维修、更换件记录，以及设备当天生产合格数", "operationId": "getEquipmentMaintenanceInfo", "requestBody": {"description": "Request parameters", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetEquipmentMaintenanceInfoRequest"}, "example": {"equipment_id": 1}}}, "required": false}, "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetEquipmentMaintenanceInfoResponse"}, "example": {"state": "success", "msgs": "获取成功", "equipment_maintenance_list": [{"equipment_id": 1, "equipment_code": "EQ001", "equipment_name": "生产设备A", "check_info": {"status": "finished", "detail": {"last_spot_check_time": "2023-06-01 08:00:00", "spot_check_plan": "日常点检", "spot_check_items": "温度检查,压力检查,润滑检查", "spot_check_result": "normal"}}, "maintain_info": {"status": "finished", "detail": {"last_maintenance_time": "2023-06-01 10:00:00", "maintenance_plan_name": "周保养", "maintenance_items": "清洁,润滑,紧固", "maintenance_result": "normal"}}, "repair_info": {"status": "no_task", "detail": {"equipment_name": "生产设备A", "last_maintenance_time": "", "repair_time": "", "repair_user": "", "removed_part": "", "installed_part": ""}}, "change_info": {"status": "no_task", "detail": {"record_date": "", "removed_part": "", "installed_part": "", "change_time": 0}}, "finish_qty": 100}]}}}}}, "security": []}}}, "components": {"schemas": {"GetEquipmentMaintenanceInfoRequest": {"type": "object", "properties": {"equipment_id": {"type": "integer", "description": "设备ID，如果不提供则返回所有设备的维护信息"}}}, "GetEquipmentMaintenanceInfoResponse": {"type": "object", "properties": {"state": {"type": "string", "description": "请求状态，success表示成功，error表示失败", "enum": ["success", "error"]}, "msgs": {"type": "string", "description": "状态消息"}, "equipment_maintenance_list": {"type": "array", "description": "设备维护信息列表", "items": {"$ref": "#/components/schemas/EquipmentMaintenance"}}}}, "EquipmentMaintenance": {"type": "object", "properties": {"equipment_id": {"type": "integer", "description": "设备ID"}, "equipment_code": {"type": "string", "description": "设备编码"}, "equipment_name": {"type": "string", "description": "设备名称"}, "check_info": {"$ref": "#/components/schemas/CheckInfo", "description": "点检信息"}, "maintain_info": {"$ref": "#/components/schemas/MaintainInfo", "description": "保养信息"}, "repair_info": {"$ref": "#/components/schemas/RepairInfo", "description": "维修信息"}, "change_info": {"$ref": "#/components/schemas/ChangeInfo", "description": "更换件信息"}, "finish_qty": {"type": "integer", "description": "当天生产合格数"}}}, "CheckInfo": {"type": "object", "properties": {"status": {"type": "string", "description": "点检状态", "enum": ["no_task", "not_started", "in_progress", "timeout", "finished"]}, "detail": {"type": "object", "description": "点检详细信息", "properties": {"last_spot_check_time": {"type": "string", "description": "最近点检时间"}, "spot_check_plan": {"type": "string", "description": "点检方案"}, "spot_check_items": {"type": "string", "description": "点检项目，多个项目用逗号分隔"}, "spot_check_result": {"type": "string", "description": "点检结果", "enum": ["normal", "abnormal"]}, "repair_status": {"type": "string", "description": "维修状态（仅当点检结果异常时返回）"}, "repair_user": {"type": "string", "description": "维修人（仅当点检结果异常时返回）"}, "repair_finish_time": {"type": "string", "description": "维修完成时间（仅当点检结果异常时返回）"}, "removed_part": {"type": "string", "description": "拆下备件（仅当点检结果异常时返回）"}, "installed_part": {"type": "string", "description": "换上备件（仅当点检结果异常时返回）"}}}}}, "MaintainInfo": {"type": "object", "properties": {"status": {"type": "string", "description": "保养状态", "enum": ["no_task", "not_started", "in_progress", "timeout", "finished"]}, "detail": {"type": "object", "description": "保养详细信息", "properties": {"last_maintenance_time": {"type": "string", "description": "最近保养时间"}, "maintenance_plan_name": {"type": "string", "description": "保养方案名称"}, "maintenance_items": {"type": "string", "description": "保养项目，多个项目用逗号分隔"}, "maintenance_result": {"type": "string", "description": "保养结果", "enum": ["normal", "abnormal"]}, "repair_status": {"type": "string", "description": "维修状态（仅当保养结果异常时返回）"}, "repair_user": {"type": "string", "description": "维修人（仅当保养结果异常时返回）"}, "repair_finish_time": {"type": "string", "description": "维修完成时间（仅当保养结果异常时返回）"}, "removed_part": {"type": "string", "description": "拆下备件（仅当保养结果异常时返回）"}, "installed_part": {"type": "string", "description": "换上备件（仅当保养结果异常时返回）"}}}}}, "RepairInfo": {"type": "object", "properties": {"status": {"type": "string", "description": "维修状态", "enum": ["no_task", "in_progress", "finished"]}, "detail": {"type": "object", "description": "维修详细信息", "properties": {"equipment_name": {"type": "string", "description": "设备名称"}, "last_maintenance_time": {"type": "string", "description": "最近保养时间"}, "repair_time": {"type": "string", "description": "维修次数"}, "repair_user": {"type": "string", "description": "维修人"}, "removed_part": {"type": "string", "description": "拆下备件，多个备件用点号分隔"}, "installed_part": {"type": "string", "description": "换上备件，多个备件用点号分隔"}}}}}, "ChangeInfo": {"type": "object", "properties": {"status": {"type": "string", "description": "更换件状态", "enum": ["no_task", "has_task"]}, "detail": {"type": "object", "description": "更换件详细信息", "properties": {"record_date": {"type": "string", "description": "最近更换时间"}, "removed_part": {"type": "string", "description": "拆下备件，多个备件用点号分隔"}, "installed_part": {"type": "string", "description": "换上备件，多个备件用点号分隔"}, "change_time": {"type": "integer", "description": "更换次数"}}}}}}}}