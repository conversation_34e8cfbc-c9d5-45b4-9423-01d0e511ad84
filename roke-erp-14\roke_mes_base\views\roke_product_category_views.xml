<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--产品类别-->
    <!--search-->
    <record id="view_roke_product_category_search" model="ir.ui.view">
        <field name="name">roke.product.category.search</field>
        <field name="model">roke.product.category</field>
        <field name="arch" type="xml">
            <search string="产品类别">
                <field name="name"/>
                <group expand="0" string="Group By">
                    <filter string="上级类别" name="group_parent_id" context="{'group_by': 'parent_id'}"/>
                </group>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_product_category_tree" model="ir.ui.view">
        <field name="name">roke.product.category.tree</field>
        <field name="model">roke.product.category</field>
        <field name="arch" type="xml">
            <tree string="产品类别">
                <field name="name"/>
                <field name="parent_id"/>
                <field name="note" optional="show"/>
                <field name="create_uid" string="创建人"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_product_category_form" model="ir.ui.view">
        <field name="name">roke.product.category.form</field>
        <field name="model">roke.product.category</field>
        <field name="arch" type="xml">
            <form string="产品类别">
                <sheet>
                    <group id="g1">
                        <group>
                            <group>
                                <field name="id" invisible="1"/>
                                <field name="name"/>
                                <field name="parent_id" domain="[('id', '!=', id)]"/>
                            </group>
                            <group>
                                <field name="erp_id"/>
                                <field name="index"/>
                            </group>
                        </group>
                        <group>
                            <group>
                                <field name="sequence_id" readoonly="1"/>
                                <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}"/>
                            </group>
                            <group>
                                <field name="note" widget="char"/>
                            </group>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>
    <!--action-->
    <record id="view_roke_product_category_action" model="ir.actions.act_window">
        <field name="name">产品类别</field>
        <field name="res_model">roke.product.category</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{}</field>
        <field name="form_view_id" ref="view_roke_product_category_form"/>
        <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
            点击左上角“创建”按钮新增一个产品类别。
          </p>
        </field>
    </record>

</odoo>
