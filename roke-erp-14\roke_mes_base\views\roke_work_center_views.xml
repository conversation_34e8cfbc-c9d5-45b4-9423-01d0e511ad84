<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--工作中心-->
    <!--search-->
    <record id="view_roke_work_center_search" model="ir.ui.view">
        <field name="name">roke.work.center.search</field>
        <field name="model">roke.work.center</field>
        <field name="arch" type="xml">
            <search string="工位">
                <field name="name"/>
                <field name="code"/>
                <field name="type_id"/>
                <field name="parent_id"/>
                <filter string="启用" name="启用" domain="[('state', '=', '启用')]"/>
                <filter string="停用" name="停用" domain="[('state', '=', '停用')]"/>
                <filter string="异常" name="异常" domain="[('state', '=', '启用')]"/>
                <separator/>
                <filter string="已归档" name="inactive" domain="[('active', '=', False)]"/>
                <group expand="0" string="Group By">
                    <filter string="状态" name="group_state" context="{'group_by': 'state'}"/>
                </group>
                <searchpanel>
                    <field name="parent_id" icon="fa-tags" enable_counters="1" expand="1"/>
                </searchpanel>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_work_center_tree" model="ir.ui.view">
        <field name="name">roke.work.center.tree</field>
        <field name="model">roke.work.center</field>
        <field name="arch" type="xml">
            <tree string="工位">
                <field name="code"/>
                <field name="name"/>
                <field name="parent_id" optional="show" string="上级工位"/>
                <field name="type_id" optional="hide" string="工位类型"/>
                <field name="note" optional="hide"/>
                <field name="state" optional="hide"/>
                <field name="create_uid" string="创建人"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_work_center_form" model="ir.ui.view">
        <field name="name">roke.work.center.form</field>
        <field name="model">roke.work.center</field>
        <field name="arch" type="xml">
            <form string="工位">
                <header/>
                <div name="button_box" class="oe_button_box">
                    <button name="show_child_action" class="oe_stat_button" icon="fa-list" type="object">
                        <field name="child_qty" widget="statinfo" string="下级数量"/>
                    </button>
                </div>
                <widget name="web_ribbon" text="归档" bg_color="bg-danger" attrs="{'invisible': [('active', '=', True)]}"/>
                <field name="active" invisible="1"/>
                <field name="id" invisible="1"/>
                <group id="g1" col="4">
                    <group>
                        <field name="name"/>
                    </group>
                    <group>
                        <field name="code"/>
                    </group>
                    <group>
                        <field name="parent_id" domain="[('id', '!=', id)]" string="上级工位"/>
                    </group>
                    <group>
                        <field name="type_id"/>
                        <field name="erp_id" invisible="1"/>
                        <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}"/>
                    </group>
                </group>
                <notebook>
                    <page string="作业规范" name="standard_item">
                        <field name="standard_item_ids" context="{'tree_view_ref': 'roke_mes_base.view_roke_work_standard_item_editable_tree'}"/>
                    </page>
                    <page string="技能要求" name="skill_leve">
                        <field name="skill_level_ids" options="{'no_create': True}"/>
                    </page>
                </notebook>
                <group id="g2">
                    <field name="note" nolabel="1" placeholder="此处可以填写备注或描述" />
                </group>
                <div class="oe_chatter">
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>
    <!--action-->
    <record id="view_roke_work_center_action" model="ir.actions.act_window">
        <field name="name">工位</field>
        <field name="res_model">roke.work.center</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{}</field>
        <field name="form_view_id" ref="view_roke_work_center_form"/>
        <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
            点击左上角“创建”按钮新增一个工位。
          </p><p>
            或者您也可以选择批量导入功能一次性导入多个工作位。
          </p>
        </field>
    </record>

</odoo>
