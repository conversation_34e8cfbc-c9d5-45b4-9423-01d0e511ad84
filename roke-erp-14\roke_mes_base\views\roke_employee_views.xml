<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--人员信息-->
    <!--search-->
    <record id="view_roke_employee_search" model="ir.ui.view">
        <field name="name">roke.employee.search</field>
        <field name="model">roke.employee</field>
        <field name="arch" type="xml">
            <search string="人员信息">
                <field name="name"/>
                <field name="code"/>
                <field name="job_number"/>
                <field name="phone"/>
                <field name="team_id"/>
                <field name="skill_level_id"/>
                <filter string="已归档" name="inactive" domain="[('active', '=', False)]"/>
                <group expand="0" string="Group By">
                    <filter string="班组" name="group_team_id" context="{'group_by': 'team_id'}"/>
<!--                    <filter string="部门" name="group_department_id" context="{'group_by': 'department_id'}"/>-->
                </group>
                <searchpanel>
                    <field name="team_id" icon="fa-users" enable_counters="1" expand="1"/>
<!--                    <field name="department_id" icon="fa-users"/>-->
                </searchpanel>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_employee_tree" model="ir.ui.view">
        <field name="name">roke.employee.tree</field>
        <field name="model">roke.employee</field>
        <field name="arch" type="xml">
            <tree string="人员信息">
                <field name="code"/>
                <field name="name"/>
                <field name="job_number"/>
                <field name="phone"/>
                <field name="team_id"/>
                <field name="skill_level_id"/>
                <field name="user_id" optional="show"/>
                <field name="note"/>
                <field name="create_uid" string="创建人"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_employee_form" model="ir.ui.view">
        <field name="name">roke.employee.form</field>
        <field name="model">roke.employee</field>
        <field name="arch" type="xml">
            <form string="人员信息">
                <header>
                    <button name="create_system_user" type="object" string="创建系统用户" class="oe_highlight"
                            attrs="{'invisible': [('user_id', '!=', False)]}" help="将使用员工工号或名称创建系统用户，默认密码000000，请确认手机号和已录入。"/>
                </header>
                <widget name="web_ribbon" text="归档" bg_color="bg-danger" attrs="{'invisible': [('active', '=', True)]}"/>
                <div class="oe_title" style="display: flex;align-items: center;">
                    <field name="image_1920" widget='image' class="oe_avatar" options='{"zoom": true, "preview_image":"image_128"}'/>
                    <h1 class="d-flex" style="margin: 0 20px;width: 400px;">
                        <field name="name" placeholder="人员名称" required="True"/>
                    </h1>
                </div>
                <group id="g1">
                    <group>
                        <group>
                            <field name="code"/>
                            <!-- <label for="team_id"/> -->
                            <field name="team_id"/>
                            <field name="team_weighted"/>
                            <!-- <div name="team_id" class="o_row">
                                <field name="team_id"/>
                                <span attrs="{'invisible': [('team_id', '=', False)]}">
                                    权重：
                                </span>
                                <span attrs="{'invisible': [('team_id', '=', False)]}">
                                    <field name="team_weighted"/>
                                </span>
                            </div> -->
                            <!-- <field name="team_id"/> -->
                            <field name="job_number"/>
                        </group>
                        <group>
                            <field name="position_id"/>
                            <field name="gender"/>
                            <field name="department_id"/>
                            <field name="is_on_job"/>
                            <field name="skill_level_id"/>
                        </group>
                    </group>
                    <group>
                        <group>
                            <field name="phone"/>
                            <field name="id_number"/>
                            <field name="registered_residence"/>
                            
                        </group>
                        <group>
                            <field name="user_id" readonly="1" attrs="{'invisible': [('user_id', '=', False)]}"/>
                            <field name="erp_id"/>
                            <field name="age"/>
                            <field name="active" widget="boolean_toggle"/>
                            <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}"/>
                        </group>
                    </group>
                </group>
                <group id="g2">
                    <field name="note" nolabel="1" placeholder="此处可以填写备注或描述" />
                </group>
                <div class="oe_chatter">
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>
    <!--action-->
    <record id="view_roke_employee_action" model="ir.actions.act_window">
        <field name="name">班组人员信息</field>
        <field name="res_model">roke.employee</field>
        <field name="view_mode">tree,form,search</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{}</field>
        <field name="form_view_id" ref="view_roke_employee_form"/>
        <field name="search_view_id" ref="view_roke_employee_search"/>
        <field name="view_ids" eval="[(5,0,0),
              (0,0,{'view_mode': 'tree','view_id': ref('view_roke_employee_tree')}),
              (0,0,{'view_mode': 'form','view_id': ref('view_roke_employee_form')})]"/>
        <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
            点击左上角“创建”按钮新增一个人员。
          </p><p>
            或者您也可以选择批量导入功能一次性导入多个人员。
          </p>
        </field>
    </record>

</odoo>
