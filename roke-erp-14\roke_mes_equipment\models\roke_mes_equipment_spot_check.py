# -*- coding: utf-8 -*-
"""
Description:
    设备点检
Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
from odoo import models, fields, api, _
from odoo.exceptions import UserError
import datetime

class RokeMesEquipmentSpotCheckPlan(models.Model):
    _name = "roke.mes.eqpt.spot.check.plan"
    _description = "设备点检方案"
    _inherit = ['mail.thread']
    _order = "id desc"

    name = fields.Char(string="名称", required=True, index=True)
    code = fields.Char(string="编号", required=True, index=True, default="新建")
    type = fields.Selection([("period", "定期检查"), ("start", "开班检查"), ("random", "随机检查")], string="检查方式", required=True, index=True, default="start")
    check_item_ids = fields.Many2many("roke.mes.eqpt.check.item", string="检查项", required=True, index=True)
    description = fields.Text(string="描述")
    # 频率
    period_type = fields.Selection([("day", "天"), ("week", "周")], string="周期", index=True, default="day")
    period = fields.Integer(string="周期")

    frequency = fields.Integer(string="点检频率", default=1)
    frequency_unit = fields.Selection([("月", "月"), ("周", "周"), ("日", "日"), ("小时", "小时")], string="点检频率单位", default="月")
    show_frequency = fields.Char(string="点检频率", compute="_compute_show_frequency")
    frequency_days = fields.Integer(string="点检频率（天）", compute="_compute_frequency_days", store=True)
    next_check_date = fields.Date(string="下次点检日期", compute="_compute_next_maintenance_date", store=True)
    # next_check_time = fields.Datetime(string="下次点检时间", compute="_compute_next_maintenance_time", store=True)
    last_check_date = fields.Date(string="上次点检日期")
    # last_check_date_time = fields.Datetime(string="上次点检时间")

    record_ids = fields.One2many("roke.mes.eqpt.spot.check.record", "check_plan_id", string="点检记录")
    equipment_ids = fields.Many2many("roke.mes.equipment", "roke_eqpt_check_plan_rel", "c_id", "e_id", string="关联设备")
    equipment_id = fields.Many2one("roke.mes.equipment", string="关联设备")
    company_id = fields.Many2one('res.company', string='公司', default=lambda self: self.env.company)
    ir_cron = fields.Many2one("ir.cron", string="自动动作")

    def _compute_show_frequency(self):
        for record in self:
            record.show_frequency = "%s%s" % (str(record.frequency), record.frequency_unit)

    # @api.depends("last_check_time")
    # def _compute_next_maintenance_time(self):
    #     # 计算下次保养日期
    #     for record in self:
    #         if record.last_check_time:
    #             record.next_check_time = record.last_check_date + datetime.timedelta(hours=1)
    #         else:
    #             record.next_check_time = fields.Datetime.now()

    @api.depends("last_check_date", "frequency_days", "ir_cron", "ir_cron.nextcall")
    def _compute_next_maintenance_date(self):
        # 计算下次保养日期
        for record in self:
            if record.ir_cron:
                record.next_check_date = (record.ir_cron.nextcall + datetime.timedelta(hours=8)).date()
            else:
                record.next_check_date = (datetime.datetime.now() + datetime.timedelta(hours=8)).date()

    @api.depends("frequency_unit", "frequency")
    def _compute_frequency_days(self):
        # 计算保养频率间隔天数
        for record in self:
            if record.frequency_unit == "月":
                unit = 30
            elif record.frequency_unit == "周":
                unit = 7
            else:
                unit = 1
            record.frequency_days = record.frequency * unit

    def name_get(self):
        return [(record.id, "%s(%s)" % (record.name, record.code)) for record in self]

    def equipment_id_holiday(self, equipment_id):
        arrangements = self.sudo().env['roke.equipment.holiday.arrangement'].search(
            [('equipment_ids', 'in', equipment_id.id)])
        tag = True
        for arr in arrangements:
            date = arr.line_ids.filtered(
                lambda
                    d: d.start_date <= datetime.datetime.today().date() and datetime.datetime.today().date() <= d.end_date)
            if date:
                tag = False
                break
        return tag

    def create_check_record(self, res):
        plan_id = self.sudo().env['roke.mes.eqpt.spot.check.plan'].browse(int(res))
        if plan_id.equipment_id.e_state not in ('waiting', 'stop') and plan_id.equipment_id.active:
            tag = self.equipment_id_holiday(plan_id.equipment_id)
            if tag:
                self.sudo().env['roke.mes.eqpt.spot.check.record'].create({
                    'check_plan_id': plan_id.id,
                    'equipment_id': plan_id.equipment_id.id,
                    'state': 'wait'
                })

    # 获取时间标识
    def get_interval_type(self, res):
        if res.frequency_unit == '月':
            interval_type = 'months'
        elif res.frequency_unit == '周':
            interval_type = 'weeks'
        elif res.frequency_unit == '日':
            interval_type = 'days'
        else:
            interval_type = 'hours'
        return interval_type

    @api.model
    def create(self, vals):
        vals["code"] = self.env['ir.sequence'].next_by_code('roke.mes.spot.check.plan.code')
        res = super(RokeMesEquipmentSpotCheckPlan, self).create(vals)
        # 自动生成定时任务
        # if res.type == 'period':
        #     interval_type = self.get_interval_type(res)
        #     cron_id = self.sudo().env['ir.cron'].create({
        #         'name': '设备点检方案[名称：%s，周期：%s]' % (res.name, str(res.frequency) + res.frequency_unit),
        #         'model_id': self.sudo().env['ir.model'].search([('model', '=', 'roke.mes.eqpt.spot.check.plan')],
        #                                                        limit=1).id,
        #         'state': 'code',
        #         'code': 'model.create_check_record(%s)' % str(res.id),
        #         'interval_number': res.frequency,
        #         'interval_type': interval_type,
        #         'numbercall': -1,
        #         'doall': True,
        #         'active': True
        #     })
        #     res.ir_cron = cron_id.id
        return res

    def unlink(self):
        # 同步删除定时任务
        # for record in self:
            # if record.ir_cron:
            #     record.ir_cron.unlink()
        res = super(RokeMesEquipmentSpotCheckPlan, self).unlink()
        return res

    def write(self, vals):
        # 同步修改定时任务
        res = super(RokeMesEquipmentSpotCheckPlan, self).write(vals)
        # if self.type == 'period':
        #     self.update_ir_cron()
        return res

    # 更新点检动作
    def update_ir_cron(self):
        for record in self:
            # 定期检查
            if record.type == 'period':
                interval_type = self.get_interval_type(record)
                if record.ir_cron:
                    record.ir_cron.write({
                        'name': '设备点检方案[名称：%s，周期：%s]' % (
                            record.name, str(record.frequency) + record.frequency_unit),
                        'interval_number': record.frequency,
                        'interval_type': interval_type
                    })
                else:
                    cron_id = self.sudo().env['ir.cron'].create({
                        'name': '设备点检方案[名称：%s，周期：%s]' % (
                            record.name, str(record.frequency) + record.frequency_unit),
                        'model_id': self.sudo().env['ir.model'].search(
                            [('model', '=', 'roke.mes.eqpt.spot.check.plan')], limit=1).id,
                        'state': 'code',
                        'code': 'model.create_check_record(%s)' % str(record.id),
                        'interval_number': record.frequency,
                        'interval_type': interval_type,
                        'numbercall': -1,
                        'doall': True,
                        'active': True
                    })
                    record.ir_cron = cron_id.id

    def execute_scheme(self):
        # 生成定检记录
        create_list = []
        for record in self:
            create_list.append({
                "check_plan_id": record.id,
                "equipment_id": record.equipment_id.id,
            })
        self.env["roke.mes.eqpt.spot.check.record"].create(create_list)

    def check_scheme_date(self):
        # 校验是否生成点简单
        today = fields.Date.context_today(self)
        # current_time = fields.Datetime.now()
        self.search([("type", "=", "period"), ("next_check_date", "<=", today)]).execute_scheme()


class RokeMesEquipmentSpotRecord(models.Model):
    _name = "roke.mes.eqpt.spot.check.record"
    _description = "设备点检记录"
    _inherit = ['mail.thread']
    _rec_name = "code"
    _order = "id desc"

    code = fields.Char('编号', default='新建')
    check_plan_id = fields.Many2one("roke.mes.eqpt.spot.check.plan", string="点检方案", required=True, index=True)
    equipment_id = fields.Many2one("roke.mes.equipment", string="设备")
    state = fields.Selection([("wait", "等待"), ("finish", "完成"), ("cancel", "取消")], string="状态", default="wait")
    item_record_ids = fields.One2many("roke.mes.eqpt.spot.check.line", "record_id", string="检查结果", required=True, index=True)
    finish_time = fields.Datetime(string="完成时间")
    estimated_completion_time = fields.Datetime(string="预计完成时间")
    start_date = fields.Datetime(string="开始时间")
    assign_user_ids = fields.Many2many("res.users", string="指派人", default=lambda self: self.env.user)
    finish_user_id = fields.Many2one("res.users", string="完成人")
    picture = fields.Binary('图片')
    normal_state = fields.Selection([('normal', '正常'), ('abnormal', '异常')], string='是否正常', default='normal')
    description = fields.Char('备注')
    is_api = fields.Boolean(string='接口创建', default=False)
    company_id = fields.Many2one('res.company', string='公司', default=lambda self: self.env.company)

    origin_project_id = fields.Many2one("roke.mes.equipment.inspection.project", string="来源")

    @api.onchange('equipment_id')
    def _onchange_equipment_id(self):
        """
        过滤无点检方案的设备
        :return:
        """
        domain = {'check_plan_id': []}
        if self.equipment_id:
            domain = {'check_plan_id': [('equipment_id', '=', self.equipment_id.id)]}
        return {"domain": domain}

    def repair_request(self):
        """
        设备报修
        录入故障描述后产生保修单
        :return:
        """
        wizard = self.env["roke.equipment.repair.request.wizard"].create({
            "equipment_id": self.equipment_id.id,
            "spot_check_record_id": self.id
        })
        view_id = self.env.ref('roke_mes_equipment.view_roke_check_equipment_repair_request_wizard_form').id
        return {
            "type": "ir.actions.act_window",
            "name": "设备报修",
            'view_id': view_id,
            "view_mode": "form",
            "view_type": "form",
            "res_model": "roke.equipment.repair.request.wizard",
            'res_id': wizard.id,
            'target': 'new',
        }

    def make_finish(self):
        """
        完成点检
        :return:
        """
        if self.item_record_ids.filtered(lambda item: item.result=="wait"):
            raise UserError("必须完成所有检查项目")
        self.write({
            "state": "finish",
            "finish_time": fields.Datetime.now(),
            "finish_user_id": self.env.user.id
        })
        self.check_plan_id.write({"last_check_date": fields.Date.context_today(self)})

    def make_wait(self):
        """
        置为等待
        :return:
        """
        self.write({
            "state": "wait",
            "finish_time": False,
            "finish_user_id": False
        })

    def make_cancel(self):
        """
        置为取消
        :return:
        """
        self.write({
            "state": "cancel"
        })

    @api.model
    def create(self, vals):
        vals["code"] = self.env['ir.sequence'].next_by_code('roke.mes.eqpt.spot.check.record.code')
        res = super(RokeMesEquipmentSpotRecord, self).create(vals)
        LineObj = self.env["roke.mes.eqpt.spot.check.line"]
        if not res.is_api:
            for item in res.check_plan_id.check_item_ids:
                LineObj.create({
                    "record_id": res.id,
                    "check_item_id": item.id,
                })
        if not res.equipment_id:
            raise UserError('设备信息不可为空')
        return res


class RokeMesEquipmentSpotLine(models.Model):
    _name = "roke.mes.eqpt.spot.check.line"
    _description = "设备点检记录明细"
    _inherit = ['mail.thread']
    _order = "id desc"

    record_id = fields.Many2one("roke.mes.eqpt.spot.check.record", string="点检记录", required=True, index=True,
                                ondelete="cascade")
    check_item_id = fields.Many2one("roke.mes.eqpt.check.item", string="检查项", required=True, index=True)

    check_value = fields.Char(string="检查值")
    input_type_id = fields.Many2one(related="check_item_id.input_type_id", string="录入方式", ondelete="restrict")
    input_type = fields.Selection(related="input_type_id.input_type", string="最终值类型")
    standard_value = fields.Char(related="check_item_id.standard_value", string="标准值")
    upper_value = fields.Float(related="check_item_id.upper_value", string="标准上限")
    lower_value = fields.Float(related="check_item_id.lower_value", string="标准下限")

    result = fields.Selection([("wait", "未检查"), ("normal", "正常"), ("anomaly", "异常"), ("fault", "故障")], string="结果判定", default="wait")
    description = fields.Text(string="描述")
    attachment_ids = fields.Many2many('ir.attachment', string='故障&异常照片')
    is_update = fields.Boolean(string='已修改', default=False)
    company_id = fields.Many2one('res.company', string='公司', default=lambda self: self.env.company)

    def name_get(self):
        return [(record.id, "%s(%s)" % (record.record_id.display_name, record.check_item_id.display_name)) for record in self]

    def execute_entrance(self):
        # 按钮执行点检项目
        state = dict(self.env.context).get("state")
        if state == "wait":
            self.write({"result": state})
            return
        else:
            wizard = self.env["roke.execute.check.item.wizard"].create({
                "check_item_id": self.id,
            })
            return {
                "type": "ir.actions.act_window",
                "name": "完成检查",
                "view_mode": "form",
                "view_type": "form",
                "res_model": "roke.execute.check.item.wizard",
                'res_id': wizard.id,
                'target': 'new',
            }

    def execute(self, state="finish", check_value=""):
        """
        执行保养明细
        :param state:
        :param description:
        :return:
        """
        self.write({
            "result": state,
            "check_value": check_value
        })
