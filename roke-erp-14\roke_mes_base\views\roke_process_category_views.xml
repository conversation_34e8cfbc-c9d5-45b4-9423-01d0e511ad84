<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--工序类别-->
    <!--search-->
    <record id="view_roke_process_category_search" model="ir.ui.view">
        <field name="name">roke.process.category.search</field>
        <field name="model">roke.process.category</field>
        <field name="arch" type="xml">
            <search string="工序类别">
                <field name="name"/>
                <filter string="已归档" name="已归档" domain="[('active', '=', False)]"/>
                <group expand="0" string="Group By">
                    <filter string="上级类别" name="group_parent_id" context="{'group_by': 'parent_id'}"/>
                </group>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_process_category_tree" model="ir.ui.view">
        <field name="name">roke.process.category.tree</field>
        <field name="model">roke.process.category</field>
        <field name="arch" type="xml">
            <tree string="工序类别">
                <field name="name"/>
                <field name="parent_id"/>
                <field name="note" optional="show"/>
                <field name="create_uid" string="创建人"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_process_category_form" model="ir.ui.view">
        <field name="name">roke.process.category.form</field>
        <field name="model">roke.process.category</field>
        <field name="arch" type="xml">
            <form string="工序类别">
                <header>
                    <button name="toggle_active" type="object" string="归档"
                            attrs="{'invisible': [('active', '!=', True)]}"/>
                    <button name="toggle_active" type="object" string="取消归档" class='oe_highlight'
                            attrs="{'invisible': [('active', '=', True)]}"/>
                </header>
                <sheet>
                    <widget name="web_ribbon" text="归档" bg_color="bg-danger" attrs="{'invisible': [('active', '=', True)]}"/>
                    <group id="g1">
                        <group>
                            <group>
                                <field name="id" invisible="1"/>
                                <field name="active" invisible="1"/>
                                <field name="name"/>
                            </group>
                            <group>
                                <field name="parent_id" domain="[('id', '!=', id)]"/>
                            </group>
                        </group>
                        <group>
                            <group>
                                <field name="note" widget="char"/>
                            </group>
                            <group>
                                <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}"/>
                            </group>
                        </group>
                    </group>
                    <notebook>
                        <page string="作业规范" name="standard_item">
                            <field name="standard_item_ids" context="{'tree_view_ref': 'roke_mes_base.view_roke_work_standard_item_editable_tree'}"/>
                        </page>
                    </notebook>
                    <group string="说明" name="说明" attrs="{'invisible': [('active', '=', True)]}">
                        <div style="color: #a0a0a0;">
                            <p>
                                ● 当前工序类别已归档，当前类别及其下级类别将不会在操作端（移动端、报工台等）显示。
                            </p>
                        </div>
                    </group>
                </sheet>
            </form>
        </field>
    </record>
    <!--action-->
    <record id="view_roke_process_category_action" model="ir.actions.act_window">
        <field name="name">工序类别</field>
        <field name="res_model">roke.process.category</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{}</field>
        <field name="form_view_id" ref="view_roke_process_category_form"/>
        <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
            点击左上角“创建”按钮新增一个工序类别。
          </p>
        </field>
    </record>

</odoo>
