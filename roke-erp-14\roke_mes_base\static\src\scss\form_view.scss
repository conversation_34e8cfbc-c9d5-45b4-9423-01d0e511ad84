.oe_title {
    color: $headings-color;
    @include media-breakpoint-up(vsm, $o-extra-grid-breakpoints) {
        padding-right: $o-innergroup-rpadding;
    }
}

.oe_avatar + .oe_title {
    padding-right: $o-avatar-size + 10;
}

.o_FormRenderer_chatterContainer {
    max-width: 100%;
}

.modal.o_legacy_dialog.o_technical_modal.show {
    // text-align: -webkit-center;
}
.modal-dialog.modal-lg:has(.only_process_planning) {
    // margin: 10px !important;
    padding: 50px !important;
    max-width: calc(90%) !important;
    height: 100%;
}
.modal-content:has(.only_process_planning) {
    height: 100%;
}
.o_form_view.o_xxl_form_view.o_form_nosheet.o_form_editable {
    height: 100%;
}
:has(.only_process_planning) {
    height: 100%;
}
.only_process_planning {
    width: 65% !important;
    flex-shrink: 0;
    height: 100%;
}
.modal-dialog.modal-lg:has(.only_process_planning) .o_form_view .o_group .o_group_col_6 {
    width: 100% !important;
}