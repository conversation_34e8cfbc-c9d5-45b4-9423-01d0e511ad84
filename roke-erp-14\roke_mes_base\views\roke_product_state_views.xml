<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--产品状态字典-->
    <!--search-->
    <record id="view_roke_product_state_search" model="ir.ui.view">
        <field name="name">roke.product.state.search</field>
        <field name="model">roke.product.state</field>
        <field name="arch" type="xml">
            <search string="产品状态">
                <field name="name"/>
                <filter name="filter_process" string="工序状态" domain="[('process', '=', True)]"/>
                <filter name="filter_not_process" string="非工序状态" domain="[('process', '!=', True)]"/>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_product_state_tree" model="ir.ui.view">
        <field name="name">roke.product.state.tree</field>
        <field name="model">roke.product.state</field>
        <field name="arch" type="xml">
            <tree string="产品状态">
                <field name="name"/>
                <field name="process"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_product_state_form" model="ir.ui.view">
        <field name="name">roke.product.state.form</field>
        <field name="model">roke.product.state</field>
        <field name="arch" type="xml">
            <form string="产品状态">
                <sheet>
                    <group id="g1">
                        <group>
                            <group>
                                <field name="name"/>
                            </group>
                            <group>
                                <field name="process"/>
                            </group>
                        </group>
                        <group>
                            <group></group>
                            <group></group>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>
    <!--action-->
    <record id="view_roke_product_state_action" model="ir.actions.act_window">
        <field name="name">产品状态</field>
        <field name="res_model">roke.product.state</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{"default_process": False, "search_default_filter_not_process": True}</field>
        <field name="form_view_id" ref="view_roke_product_state_form"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                点击左上角“创建”按钮新增一个产品状态。
            </p>
        </field>
    </record>

</odoo>