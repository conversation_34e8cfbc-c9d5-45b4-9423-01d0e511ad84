from odoo import api, fields, models


class RokeSparePart(models.Model):
    _name = "roke.spare.part"
    _description = "备件"

    name = fields.Char(string="名称")
    code = fields.Char(string="编号")
    model = fields.Char(string="型号")
    uom_id = fields.Many2one("roke.uom", string="单位")
    theoretical_life = fields.Float(string="理论寿命", help="备件的理论使用寿命，单位根据备件类型而定（如小时、天数、次数等）")
    manufacturer = fields.Char(string="厂家", help="备件制造商或供应商名称")
    note = fields.Text(string="备注")
