from odoo import api, fields, models


class RokeSparePart(models.Model):
    _name = "roke.spare.part"
    _description = "备件"

    name = fields.Char(string="名称")
    code = fields.Char(string="编号")
    model = fields.Char(string="型号")
    uom_id = fields.Many2one("roke.uom", string="单位")
    theoretical_life = fields.Float(string="理论寿命", help="备件的理论使用寿命，单位根据备件类型而定（如年，月，日等）")
    life_unit = fields.Selection([("year", "年"), ("month", "月"), ("day", "日")], string="寿命单位", default="day")
    manufacturer = fields.Char(string="厂家", help="备件制造商或供应商名称")
    image = fields.Binary(string="图片", help="备件图片，便于识别和管理")
    note = fields.Text(string="备注")
