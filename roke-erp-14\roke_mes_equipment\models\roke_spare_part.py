from odoo import api, fields, models


class RokeSparePart(models.Model):
    _name = "roke.spare.part"
    _description = "备件"

    name = fields.Char(string="名称", size=20, required=True)
    code = fields.Char(string="编号", default="New")
    model = fields.Char(string="型号", size=20)
    uom_id = fields.Many2one("roke.uom", string="单位", required=True)
    theoretical_life = fields.Integer(string="理论寿命", help="备件的理论使用寿命，单位根据备件类型而定（如年，月等）")
    life_unit = fields.Selection([("year", "年"), ("month", "月")], string="寿命单位", default="month")
    manufacturer = fields.Char(string="厂家", help="备件制造商或供应商名称", size=20)
    image = fields.Binary(string="图片", help="备件图片，便于识别和管理")
    note = fields.Text(string="备注")

    # 关联字段
    usage_record_ids = fields.One2many('roke.spare.part.usage.record', 'spare_part_id', string="使用记录")
    usage_record_count = fields.Integer(string="已使用数量", compute="_compute_usage_record_count")

    @api.model
    def create(self, vals):
        if vals.get('code', 'New') == 'New':
            vals['code'] = self.env['ir.sequence'].next_by_code('roke.spare.part') or 'New'
        return super(RokeSparePart, self).create(vals)

    @api.depends('usage_record_ids')
    def _compute_usage_record_count(self):
        """计算使用记录数量"""
        for record in self:
            record.usage_record_count = len(record.usage_record_ids)

    def action_view_usage_records(self):
        """查看使用记录"""
        self.ensure_one()
        return {
            'type': 'ir.actions.act_window',
            'name': f'{self.name} - 使用记录',
            'res_model': 'roke.spare.part.usage.record',
            'view_mode': 'tree,form',
            'domain': [('spare_part_id', '=', self.id)],
            'context': {'default_spare_part_id': self.id},
        }
