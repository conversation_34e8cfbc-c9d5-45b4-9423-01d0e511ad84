# -*- coding: utf-8 -*-
"""
Description:
    继承设备、工作中心
Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
from odoo import models, fields, api, _
from odoo.exceptions import UserError
import json
import qrcode
import io
import base64


def make_qr(string):
    qr = qrcode.QRCode(
        version=4,  # 生成二维码尺寸的大小 1-40 1:21*21（21+(n-1)*4）
        error_correction=qrcode.constants.ERROR_CORRECT_M,  # L:7% M:15% Q:25% H:30%
        box_size=8,  # 每个格子的像素大小
        border=2,  # 边框的格子宽度大小
    )
    qr.add_data(string)
    qr.make(fit=True)
    img = qr.make_image()
    buf = io.BytesIO()
    img.save(buf)
    img_stream = buf.getvalue()
    return base64.b64encode(img_stream)


class RokeMesEquipmentCategory(models.Model):
    _name = 'roke.mes.equipment.category'
    _inherit = ['mail.thread']
    _description = '设备类别'

    name = fields.Char(string='设备类别名称', required=True)
    note = fields.Text(string='描述')
    parent_id = fields.Many2one("roke.mes.equipment.category", string="上级类别")
    company_id = fields.Many2one('res.company', string='公司', default=lambda self: self.env.company)
    sequence = fields.Integer(string="序号", default=1)


class RokeMesEquipment(models.Model):
    _name = "roke.mes.equipment"
    _inherit = ['mail.thread']
    _description = '设备信息'
    _order = 'create_date desc'

    name = fields.Char(string='设备名称', size=20, required=True, track_visibility='onchange')
    code = fields.Char(string="设备编号", size=20, track_visibility='onchange')
    category_id = fields.Many2one('roke.mes.equipment.category', string='设备类别', track_visibility='onchange')
    active = fields.Boolean(default=True)
    work_center_id = fields.Many2one("roke.work.center", string="工作中心", track_visibility='onchange')
    spot_plan_ids = fields.Many2many("roke.mes.eqpt.spot.check.plan", "roke_eqpt_check_plan_rel", "e_id", "c_id", string="点检方案", track_visibility='onchange')
    last_spot_date = fields.Date(string="上次点检时间")
    location = fields.Char(string="当前位置")
    index_code = fields.Char(string="整机编码")
    manufacturer = fields.Char(string="厂家")
    manufacturer_code = fields.Char(string="出厂编号")
    specification = fields.Char(string="规格型号", size=20, track_visibility='onchange')
    manufacture_date = fields.Date(string="生产日期")
    warranty_date = fields.Date(string="保修期截止")
    register_code = fields.Char(string="注册代码")
    # 建档
    create_archives_user_id = fields.Many2one("res.users", string="建档人")
    archives_code = fields.Char(string="建档号")
    archives_date = fields.Date(string="建档日期")
    use_permit_code = fields.Char(string="使用证编号")
    # 定检
    test_org_id = fields.Many2one("roke.mes.equipment.test.org", string="检验机构")
    last_test_date = fields.Date(string="本次检验日期")
    next_test_date = fields.Date(string="下次检验日期")
    user_id = fields.Many2one("res.users", string="负责人", help="安全管理人员")
    # periodic_test_ids = fields.One2many("roke.mes.equipment.periodic.test", "equipment_id", string="设备定期检验记录")

    note = fields.Text(string='描述/说明')
    # 维修记录
    maintenance_ids = fields.One2many("roke.mes.maintenance.order", "equipment_id", string="保养记录", domain=[("type", "=", "maintain")])
    # 保养记录
    repair_ids = fields.One2many("roke.mes.maintenance.order", "equipment_id", string="维修记录", domain=[("type", "=", "repair")])
    # 在修中
    in_repair = fields.Boolean(string="在修")
    # 特种附件
    is_part = fields.Boolean(string="特种附件")
    parent_eqpt_id = fields.Many2one("roke.mes.equipment", string="归属设备")
    # child_eqpt_ids = fields.One2many("roke.mes.equipment", "parent_eqpt_id", string="附件清单")
    auxiliary_equipment_lines = fields.One2many("roke.equipment.auxiliary", "auxiliary_id", string="附属设备信息")
    accessory_equipment_lines = fields.One2many("roke.equipment.accessory", "accessory_id", string="配件信息")
    change_record_ids = fields.One2many('roke.mes.equipment.change.record', 'e_id', '更换记录')
    qr_image = fields.Image("二维码")
    e_state = fields.Selection([('闲置', '闲置'), ('在用', '在用'), ('报废', '报废'), ('报修', '报修')],
                               default="在用", string='设备状态', copy=False, tracking=True)
    company_id = fields.Many2one('res.company', string='公司', default=lambda self: self.env.company)
    is_has_scrapped = fields.Boolean(string="是否含有报废单")
    price = fields.Float(string="设备单价")
    active_date = fields.Date(string="启用日期")

    data_acquisition_code = fields.Char(string="数据采集编号", track_visibility='onchange')

    def btn_cr_qr_image(self):
        for record in self:
            # 生成
            qr_data = json.dumps({
                'id': record.id,
                'code': record.code,
                'name': record.name,
            }, ensure_ascii=False)
            record.qr_image = make_qr(qr_data)

    def repair_request(self):
        """
        设备报修
        录入故障描述后产生保修单
        :return:
        """
        wizard = self.env["roke.equipment.repair.request.wizard"].create({
            "equipment_id": self.id
        })
        view_id = self.env.ref('roke_mes_equipment.view_roke_equipment_repair_request_wizard_form').id
        return {
            "type": "ir.actions.act_window",
            "name": "设备报修",
            'view_id': view_id,
            "view_mode": "form",
            "view_type": "form",
            "res_model": "roke.equipment.repair.request.wizard",
            'res_id': wizard.id,
            'target': 'new',
        }

class RokeEquipmentAuxiliary(models.Model):
    _name = "roke.equipment.auxiliary"
    _description = '附属设备信息'

    auxiliary_id = fields.Many2one("roke.mes.equipment", string="设备", required=True, ondelete='cascade')
    equipment_name = fields.Char(string='设备名称', required=True, track_visibility='onchange')
    specification = fields.Char(string="规格型号")
    uom_id = fields.Many2one("roke.uom", string="单位")
    acceptance_qty = fields.Integer(string="验收数量")
    note = fields.Text(string='备注')

class RokeEquipmentAccessory(models.Model):
    _name = "roke.equipment.accessory"
    _description = '配件信息'

    accessory_id = fields.Many2one("roke.mes.equipment", string="设备", required=True, ondelete='cascade')
    accessory_name = fields.Char(string='配件名称', required=True, track_visibility='onchange')
    specification = fields.Char(string="规格型号")
    uom_id = fields.Many2one("roke.uom", string="单位")
    acceptance_qty = fields.Integer(string="验收数量")
    note = fields.Text(string='备注')

class RokeMesEquipmentChangeRecord(models.Model):
    _name = "roke.mes.equipment.change.record"
    _inherit = ['mail.thread']
    _description = '更换件记录'

    code = fields.Char(string="编号", required=True, 
                       default=lambda self: self.env['ir.sequence'].next_by_code('roke.mes.equipment.change.record.code'))
    name = fields.Char('拆下配件')
    e_id = fields.Many2one('roke.mes.equipment', '针对设备')
    new_name = fields.Char('新装配件')
    sequence = fields.Integer('序号')
    record_date = fields.Datetime('更换时间')
    change_user_id = fields.Many2one('res.users', '更换人')
    company_id = fields.Many2one('res.company', string='公司', default=lambda self: self.env.company)
    item_id = fields.Many2one("roke.mes.maintenance.item", string="保养项目")

    def action_save(self):
        pass


class RokeMesInputType(models.Model):
    _name = "roke.mes.input.type"
    _description = "输入类型"
    _order = "id desc"

    name = fields.Char(string="类型描述", required=True)
    index = fields.Char(string="系统标识", required=True)
    input_type = fields.Selection([("text", "文本"), ("float", "小数"), ("int", "整数"), ("select", "选择")], string="最终值类型", required=True, default="text")
    company_id = fields.Many2one('res.company', string='公司', default=lambda self: self.env.company)

    def name_get(self):
        return [(record.id, "%s(%s)" % (record.name, dict(self.fields_get(allfields=["input_type"])["input_type"]['selection'])[record.input_type])) for record in self]

