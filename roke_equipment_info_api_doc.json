{"openapi": "3.0.1", "info": {"title": "ROKE Equipment API", "description": "API documentation for ROKE Equipment module", "version": "1.0.0"}, "servers": [{"url": "http://localhost:8069", "description": "Local development server"}], "paths": {"/roke/equipment/info": {"post": {"tags": ["Equipment"], "summary": "获取设备基础信息", "description": "获取设备的详细信息，包括基本信息、维修记录、保养记录、点检记录和更换件记录", "operationId": "getEquipmentInfo", "requestBody": {"description": "Request parameters", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetEquipmentInfoRequest"}, "example": {"equipment_id": 1}}}, "required": true}, "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetEquipmentInfoResponse"}}}}}, "security": [{"odooAuth": []}]}}}, "components": {"schemas": {"GetEquipmentInfoRequest": {"type": "object", "required": ["equipment_id"], "properties": {"equipment_id": {"type": "integer", "description": "设备ID"}}}, "GetEquipmentInfoResponse": {"type": "object", "properties": {"state": {"type": "string", "description": "请求状态", "enum": ["success", "error"]}, "msgs": {"type": "string", "description": "状态消息"}, "equipment": {"$ref": "#/components/schemas/EquipmentInfo"}}}, "EquipmentInfo": {"type": "object", "properties": {"id": {"type": "integer", "description": "设备ID"}, "name": {"type": "string", "description": "设备名称"}, "code": {"type": "string", "description": "设备编号"}, "category": {"type": "string", "description": "设备类别"}, "specification": {"type": "string", "description": "规格型号"}, "e_state": {"type": "string", "description": "设备状态", "enum": ["闲置", "在用", "报废", "报修"]}, "plant_name": {"type": "string", "description": "车间名称"}, "workshop_name": {"type": "string", "description": "产线名称"}, "work_center_name": {"type": "string", "description": "工位名称"}, "user_name": {"type": "string", "description": "设备负责人"}, "manufacture_date": {"type": "string", "format": "date", "description": "生产日期"}, "warranty_date": {"type": "string", "format": "date", "description": "保修期截止日期"}, "repair_record_list": {"type": "array", "description": "维修记录列表（最近2条）", "items": {"$ref": "#/components/schemas/RepairRecord"}}, "maintain_record_list": {"type": "array", "description": "保养记录列表（最近2条）", "items": {"$ref": "#/components/schemas/MaintenanceRecord"}}, "check_record_list": {"type": "array", "description": "点检记录列表（最近2条）", "items": {"$ref": "#/components/schemas/CheckRecord"}}, "change_record_list": {"type": "array", "description": "更换件记录列表（最近2条）", "items": {"$ref": "#/components/schemas/ChangeRecord"}}}}, "RepairRecord": {"type": "object", "properties": {"id": {"type": "integer", "description": "维修记录ID"}, "report_user_id": {"type": "integer", "description": "报修人ID"}, "report_user_name": {"type": "string", "description": "报修人姓名"}, "repair_user_id": {"type": "integer", "description": "维修人ID"}, "repair_user_name": {"type": "string", "description": "维修人姓名"}, "report_time": {"type": "string", "format": "date", "description": "报修时间"}, "fault_description": {"type": "string", "description": "故障描述"}, "state": {"type": "string", "description": "维修状态"}, "maintenance_scheme": {"type": "string", "description": "维修方案"}, "finish_time": {"type": "string", "format": "date-time", "description": "完成时间"}, "equipment_name": {"type": "string", "description": "设备名称"}, "priority": {"type": "string", "description": "优先级"}, "last_maintenance_date": {"type": "string", "format": "date", "description": "上次维护日期"}, "use_time": {"type": "number", "description": "使用时间"}, "item_list": {"type": "array", "description": "维修项目列表", "items": {"type": "object"}}}}, "MaintenanceRecord": {"type": "object", "description": "保养记录，结构与维修记录类似"}, "CheckRecord": {"type": "object", "properties": {"id": {"type": "integer", "description": "点检记录ID"}, "code": {"type": "string", "description": "点检记录编号"}, "check_plan_id": {"type": "integer", "description": "点检方案ID"}, "check_plan_name": {"type": "string", "description": "点检方案名称"}, "assign_user_name": {"type": "string", "description": "指派人员"}, "finish_time": {"type": "string", "format": "date", "description": "完成时间"}, "state": {"type": "string", "description": "点检状态"}, "description": {"type": "string", "description": "描述"}, "finish_user_id": {"type": "integer", "description": "完成人ID"}, "finish_user_name": {"type": "string", "description": "完成人姓名"}, "item_record_names": {"type": "string", "description": "点检项目名称（逗号分隔）"}, "item_record_list": {"type": "array", "description": "点检项目详细列表", "items": {"type": "object", "properties": {"id": {"type": "integer", "description": "点检项目记录ID"}, "check_item_id": {"type": "integer", "description": "点检项目ID"}, "check_item_name": {"type": "string", "description": "点检项目名称"}, "check_value": {"type": "string", "description": "检查值"}, "result": {"type": "string", "description": "检查结果"}, "description": {"type": "string", "description": "描述"}}}}}}, "ChangeRecord": {"type": "object", "properties": {"id": {"type": "integer", "description": "更换记录ID"}, "code": {"type": "string", "description": "更换记录编号"}, "name": {"type": "string", "description": "拆下部件名称"}, "new_name": {"type": "string", "description": "新部件名称"}, "record_date": {"type": "string", "format": "date", "description": "更换日期"}, "change_user_id": {"type": "integer", "description": "更换人ID"}, "change_user_name": {"type": "string", "description": "更换人姓名"}}}}, "securitySchemes": {"odooAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "Odoo用户认证"}}}}