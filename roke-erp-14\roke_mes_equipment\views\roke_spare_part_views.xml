<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--备件管理-->
    <!--search-->
    <record id="view_roke_spare_part_search" model="ir.ui.view">
        <field name="name">roke.spare.part.search</field>
        <field name="model">roke.spare.part</field>
        <field name="arch" type="xml">
            <search string="备件">
                <field name="name"/>
                <field name="code"/>
                <field name="model"/>
                <field name="manufacturer"/>
                <group expand="0" string="Group By">
                    <filter string="厂家" name="manufacturer" context="{'group_by': 'manufacturer'}"/>
                    <filter string="单位" name="uom_id" context="{'group_by': 'uom_id'}"/>
                </group>
            </search>
        </field>
    </record>
    
    <!--tree-->
    <record id="view_roke_spare_part_tree" model="ir.ui.view">
        <field name="name">roke.spare.part.tree</field>
        <field name="model">roke.spare.part</field>
        <field name="arch" type="xml">
            <tree string="备件">
                <field name="image" widget="image" options="{'size': [40, 40]}" string="图片"/>
                <field name="code"/>
                <field name="name"/>
                <field name="model"/>
                <field name="manufacturer"/>
                <field name="theoretical_life"/>
                <field name="life_unit"/>
                <field name="uom_id"/>
            </tree>
        </field>
    </record>
    
    <!--form-->
    <record id="view_roke_spare_part_form" model="ir.ui.view">
        <field name="name">roke.spare.part.form</field>
        <field name="model">roke.spare.part</field>
        <field name="arch" type="xml">
            <form string="备件">
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <field name="image" widget="image" class="oe_avatar"/>
                    </div>
                    <group>
                        <group string="基本信息">
                            <field name="name" required="1"/>
                            <field name="code"/>
                            <field name="model"/>
                            <field name="uom_id"/>
                        </group>
                        <group string="技术参数">
                            <field name="manufacturer"/>
                            <field name="theoretical_life"/>
                            <field name="life_unit"/>
                        </group>
                    </group>
                    <group string="备注">
                        <field name="note" nolabel="1"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>
    
    <!--action-->
    <record id="action_roke_spare_part" model="ir.actions.act_window">
        <field name="name">备件管理</field>
        <field name="res_model">roke.spare.part</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="view_roke_spare_part_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                创建第一个备件记录
            </p>
            <p>
                在这里可以管理所有的备件信息，包括备件的基本信息、技术参数等。
            </p>
        </field>
    </record>
</odoo>
