import requests
import json

# 获取 access_token（有效期2小时，建议缓存）
def get_access_token(appid, secret):
    url = f'https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={appid}&secret={secret}'
    response = requests.get(url)
    result = response.json()
    return result.get('access_token')

# 发送订阅消息
def send_wechat_message(appid, secret, openid, template_id, page, data):
    access_token = get_access_token(appid, secret)
    url = f'https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token={access_token}'

    payload = {
        "touser": openid,
        "template_id": template_id,
        "page": page,  # 小程序内页面路径
        "data": data
    }

    headers = {"Content-Type": "application/json"}
    response = requests.post(url, headers=headers, data=json.dumps(payload))
    return response.json()


appid = 'wxbef52518bdfb8faa'
secret = 'a773b05cab3bc04eb135ffc6dcc03cb5'
access_token = get_access_token(appid, secret)
print(f"access_token:{access_token}")
openid = '用户的OPENID'
template_id = '模板ID'
page = 'pages/index/index'  # 小程序页面路径
data = {
    "thing1": {"value": "您的订单已完成"},
    "time2": {"value": "2025年05月13日"}
}

# res = send_wechat_message(appid, secret, openid, template_id, page, data)
# print(res)
