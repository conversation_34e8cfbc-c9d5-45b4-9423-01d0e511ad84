# -*- coding: utf-8 -*-"""
# Description:
#     加密鉴权
# Versions:
#     Created by www.rokedata.com<HuChuanwei>
# """
from odoo import models, fields, api, _
import requests
import logging
import json
from odoo.exceptions import UserError
_logger = logging.getLogger(__name__)


class InheritResUsers(models.Model):
    _inherit = "res.users"

    openid = fields.Char(string='WeChat OpenID')

    def _compute_session_token(self, sid):
        """
            保存session id到roke.session
        """
        res = super(InheritResUsers, self)._compute_session_token(sid)
        RokeSession = self.env["roke.session"]
        if not RokeSession.search([("sid", "=", sid)]):
            RokeSession.create({"sid": sid, "user_id": self.id})
        return res

    def _sync_cloud(self, requests_url, data):
        # 接口调用
        # TODO 异常处理
        requests.post(
            requests_url, data=json.dumps(data), headers={"Content-Type": "application/json"}
        )

    def sync_user_roke_cloud(self, operation="edit"):
        # 账套信息同步至云端
        database_uuid = self.env["ir.config_parameter"].sudo().get_param("database.uuid")
        requests_url = self.env["ir.config_parameter"].sudo().get_param("roke.mes.cloud.url")
        if operation == "create":
            users = [{
                "id": self.id,
                "name": self.name,
                "login": self.login,
                "phone": self.phone,
                "password": self.roke_password
            }]
            self._sync_cloud(
                "%s/roke/mes/cloud/add_users" % requests_url,
                {"database_uuid": database_uuid, "users": users}
            )
        elif operation == "delete":
            self._sync_cloud(
                "%s/roke/mes/cloud/delete_users" % requests_url,
                {"database_uuid": database_uuid, "users": self.mapped("id")}
            )
        else:
            for record in self:
                users = [{
                    "id": record.id,
                    "name": record.name,
                    "login": record.login,
                    "phone": record.phone,
                    "password": record.roke_password
                }]
                self._sync_cloud(
                    "%s/roke/mes/cloud/edit_users" % requests_url,
                    {"database_uuid": database_uuid, "users": users}
                )

    def create(self, vals):
        database_uuid = self.env["ir.config_parameter"].sudo().get_param("database.uuid")
        requests_url = self.env["ir.config_parameter"].sudo().get_param("roke.mes.cloud.url")
        # result = requests.post(
        #     "%s/roke/mes/cloud/max_user_count" % requests_url,
        #     data=json.dumps({"database_uuid": database_uuid}),
        #     headers={"Content-Type": "application/json"}
        # )
        try:
            result = requests.post(
                f"{requests_url}/roke/mes/cloud/max_user_count",
                json={"database_uuid": database_uuid},
                # timeout=(connect_timeout, read_timeout)  # 可以设置连接和读取超时时间，单位秒
            )
            max_user = json.loads(result.content).get("result")
        except requests.RequestException as e:
            _logger.error(e)
            max_user = 99999

        count = len(self.search([("active", "=", True)]))
        if count >= max_user:
            raise UserError("用户数超出限制，禁止创建！请联系系统服务商（www.rokedata.com）")
        else:
            if not vals.get("email"):  # 未设置邮箱的新用户默认给个虚拟的邮箱账号
                vals["email"] = "%<EMAIL>" % vals.get("login")
            res = super(InheritResUsers, self).create(vals)
            res.sync_user_roke_cloud(operation="create")
            return res

    def write(self, vals):
        if vals.__contains__("password"):
            vals["roke_password"] = vals["password"]
        res = super(InheritResUsers, self).write(vals)
        if vals.get("name") or vals.get("login") or vals.get("roke_password") or vals.get("phone"):
            # 同步编辑用户到云端
            self.sync_user_roke_cloud(operation="edit")
        return res

    def unlink(self):
        # 同步删除用户到云端
        self.sync_user_roke_cloud(operation="delete")
        return super(InheritResUsers, self).unlink()

    @api.model
    def _get_login_domain(self, login):
        return ['|', ('login', '=', login), ('phone', '=', login)]


class RokeWxToken(models.Model):
    _name = "roke.wx.token"

    wx_token = fields.Char(string="微信token")
    get_time = fields.Datetime(string="token获取时间")