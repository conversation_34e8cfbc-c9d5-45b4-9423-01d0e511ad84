{"openapi": "3.0.1", "info": {"title": "ROKE Spare Part Usage Records API", "description": "API documentation for ROKE Spare Part Usage Records module", "version": "1.0.0"}, "servers": [{"url": "http://localhost:8069", "description": "Local development server"}], "paths": {"/roke/spare_part/usage_records": {"post": {"tags": ["Spare Part Usage Records"], "summary": "根据备件ID查询备件使用记录", "description": "根据备件ID查询备件使用记录，返回针对设备、拆下备件、更换时间、备件寿命到期时间等信息，支持分页", "operationId": "getSparePartUsageRecords", "requestBody": {"description": "Request parameters", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetSparePartUsageRecordsRequest"}, "example": {"spare_part_id": 1, "page": 1, "page_size": 10}}}, "required": true}, "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetSparePartUsageRecordsResponse"}, "example": {"state": "success", "msgs": "获取成功", "data": {"spare_part_info": {"id": 1, "name": "深沟球轴承", "code": "SP000001", "model": "6205-2RS", "manufacturer": "SKF"}, "usage_records": [{"id": 1, "equipment_id": 5, "equipment_name": "生产设备A", "equipment_code": "EQ001", "removed_part_id": 2, "removed_part_name": "旧轴承", "removed_part_code": "SP000002", "replacement_time": "2023-06-01 10:30:00", "expiry_time": "2025-06-01 10:30:00", "remaining_days": 365, "usage_days": 30, "maintenance_order_id": 10, "maintenance_order_code": "MO000010", "spare_part_name": "深沟球轴承", "spare_part_code": "SP000001"}], "pagination": {"page": 1, "page_size": 10, "total_count": 5, "total_pages": 1, "has_next": false, "has_prev": false}}}}}}}, "security": [{"odooAuth": []}]}}}, "components": {"schemas": {"GetSparePartUsageRecordsRequest": {"type": "object", "required": ["spare_part_id"], "properties": {"spare_part_id": {"type": "integer", "description": "备件ID"}, "page": {"type": "integer", "description": "页码，默认1", "default": 1, "minimum": 1}, "page_size": {"type": "integer", "description": "每页数量，默认10", "default": 10, "minimum": 1, "maximum": 100}}}, "GetSparePartUsageRecordsResponse": {"type": "object", "properties": {"state": {"type": "string", "description": "请求状态", "enum": ["success", "error"]}, "msgs": {"type": "string", "description": "状态消息"}, "data": {"$ref": "#/components/schemas/UsageRecordsData"}}}, "UsageRecordsData": {"type": "object", "properties": {"spare_part_info": {"$ref": "#/components/schemas/SparePartInfo"}, "usage_records": {"type": "array", "description": "使用记录列表", "items": {"$ref": "#/components/schemas/UsageRecord"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}, "SparePartInfo": {"type": "object", "properties": {"id": {"type": "integer", "description": "备件ID"}, "name": {"type": "string", "description": "备件名称"}, "code": {"type": "string", "description": "备件编号"}, "model": {"type": "string", "description": "型号"}, "manufacturer": {"type": "string", "description": "厂家"}}}, "UsageRecord": {"type": "object", "properties": {"id": {"type": "integer", "description": "使用记录ID"}, "equipment_id": {"type": "integer", "description": "针对设备ID"}, "equipment_name": {"type": "string", "description": "针对设备名称"}, "equipment_code": {"type": "string", "description": "针对设备编号"}, "removed_part_id": {"type": "integer", "description": "拆下备件ID"}, "removed_part_name": {"type": "string", "description": "拆下备件名称"}, "removed_part_code": {"type": "string", "description": "拆下备件编号"}, "replacement_time": {"type": "string", "format": "date-time", "description": "更换时间，格式：YYYY-MM-DD HH:MM:SS"}, "expiry_time": {"type": "string", "format": "date-time", "description": "备件寿命到期时间，格式：YYYY-MM-DD HH:MM:SS"}, "remaining_days": {"type": "integer", "description": "剩余天数"}, "usage_days": {"type": "integer", "description": "已使用天数"}, "maintenance_order_id": {"type": "integer", "description": "关联维修单ID"}, "maintenance_order_code": {"type": "string", "description": "关联维修单编号"}, "spare_part_name": {"type": "string", "description": "备件名称"}, "spare_part_code": {"type": "string", "description": "备件编号"}}}, "Pagination": {"type": "object", "properties": {"page": {"type": "integer", "description": "当前页码"}, "page_size": {"type": "integer", "description": "每页数量"}, "total_count": {"type": "integer", "description": "总记录数"}, "total_pages": {"type": "integer", "description": "总页数"}, "has_next": {"type": "boolean", "description": "是否有下一页"}, "has_prev": {"type": "boolean", "description": "是否有上一页"}}}}, "securitySchemes": {"odooAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "Odoo用户认证"}}}}